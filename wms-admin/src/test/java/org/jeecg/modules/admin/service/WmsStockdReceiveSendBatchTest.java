package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.service.impl.WmsStockdReceiveSendServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 库存收发批量审核功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class WmsStockdReceiveSendBatchTest {

    /**
     * 测试批量审核功能的数据结构
     */
    @Test
    public void testBatchAuditDataStructure() {
        // 创建测试数据
        List<WmsStockdReceiveSend> testList = createTestData();

        // 验证数据结构
        for (WmsStockdReceiveSend receiveSend : testList) {
            System.out.println("单据号: " + receiveSend.getBillNo());
            System.out.println("物料编号: " + receiveSend.getItemCode());
            System.out.println("数量: " + receiveSend.getQuantity());
            System.out.println("批号: " + receiveSend.getBatchCode());
            System.out.println("单据类型: " + receiveSend.getBillType());
            System.out.println("账套: " + receiveSend.getAccountCode());
            System.out.println("---");
        }
    }

    /**
     * 创建测试数据
     */
    private List<WmsStockdReceiveSend> createTestData() {
        List<WmsStockdReceiveSend> list = new ArrayList<>();

        // 测试数据1 - 入库单
        WmsStockdReceiveSend receive1 = new WmsStockdReceiveSend();
        receive1.setId("1");
        receive1.setBillNo("RK202412280001");
        receive1.setBillType("1"); // 入库
        receive1.setItemCode("MAT001");
        receive1.setItemName("测试物料001");
        receive1.setQuantity(100.0);
        receive1.setBatchCode("BATCH001");
        receive1.setWarehouseCode("WH001");
        receive1.setAccountCode("ACC001");
        receive1.setTransactionType("RECEIVE");
        receive1.setPostDate(new Date());
        receive1.setUnitPrice(10.5);
        receive1.setApproveStatus("0");
        receive1.setErpSync("0");
        list.add(receive1);

        // 测试数据2 - 出库单
        WmsStockdReceiveSend send1 = new WmsStockdReceiveSend();
        send1.setId("2");
        send1.setBillNo("CK202412280001");
        send1.setBillType("2"); // 出库
        send1.setItemCode("MAT002");
        send1.setItemName("测试物料002");
        send1.setQuantity(50.0);
        send1.setBatchCode("BATCH002");
        send1.setWarehouseCode("WH001");
        send1.setAccountCode("ACC001");
        send1.setTransactionType("SEND");
        send1.setPostDate(new Date());
        send1.setUnitPrice(15.0);
        send1.setApproveStatus("0");
        send1.setErpSync("0");
        list.add(send1);

        // 测试数据3 - 同账套不同物料
        WmsStockdReceiveSend receive2 = new WmsStockdReceiveSend();
        receive2.setId("3");
        receive2.setBillNo("RK202412280002");
        receive2.setBillType("1"); // 入库
        receive2.setItemCode("MAT003");
        receive2.setItemName("测试物料003");
        receive2.setQuantity(200.0);
        receive2.setBatchCode("BATCH003");
        receive2.setWarehouseCode("WH002");
        receive2.setAccountCode("ACC001"); // 同一账套
        receive2.setTransactionType("RECEIVE");
        receive2.setPostDate(new Date());
        receive2.setUnitPrice(8.0);
        receive2.setApproveStatus("0");
        receive2.setErpSync("0");
        list.add(receive2);

        return list;
    }

    /**
     * 创建多样化测试数据（包含不同事务类型）
     */
    private List<WmsStockdReceiveSend> createDiverseTestData() {
        List<WmsStockdReceiveSend> list = new ArrayList<>();

        // 测试数据1 - 入库单，事务类型RECEIVE
        WmsStockdReceiveSend receive1 = new WmsStockdReceiveSend();
        receive1.setId("1");
        receive1.setBillNo("RK202412280001");
        receive1.setBillType("1"); // 入库
        receive1.setItemCode("MAT001");
        receive1.setItemName("测试物料001");
        receive1.setQuantity(100.0);
        receive1.setBatchCode("BATCH001");
        receive1.setWarehouseCode("WH001");
        receive1.setAccountCode("ACC001");
        receive1.setTransactionType("RECEIVE"); // 事务类型1
        receive1.setPostDate(new Date());
        receive1.setUnitPrice(10.5);
        list.add(receive1);

        // 测试数据2 - 入库单，事务类型PURCHASE
        WmsStockdReceiveSend receive2 = new WmsStockdReceiveSend();
        receive2.setId("2");
        receive2.setBillNo("RK202412280002");
        receive2.setBillType("1"); // 入库
        receive2.setItemCode("MAT002");
        receive2.setItemName("测试物料002");
        receive2.setQuantity(200.0);
        receive2.setBatchCode("BATCH002");
        receive2.setWarehouseCode("WH001");
        receive2.setAccountCode("ACC001");
        receive2.setTransactionType("PURCHASE"); // 事务类型2
        receive2.setPostDate(new Date());
        receive2.setUnitPrice(15.0);
        list.add(receive2);

        // 测试数据3 - 出库单，事务类型SEND
        WmsStockdReceiveSend send1 = new WmsStockdReceiveSend();
        send1.setId("3");
        send1.setBillNo("CK202412280001");
        send1.setBillType("2"); // 出库
        send1.setItemCode("MAT003");
        send1.setItemName("测试物料003");
        send1.setQuantity(50.0);
        send1.setBatchCode("BATCH003");
        send1.setWarehouseCode("WH001");
        send1.setAccountCode("ACC001");
        send1.setTransactionType("SEND"); // 事务类型3
        send1.setPostDate(new Date());
        send1.setUnitPrice(12.0);
        list.add(send1);

        // 测试数据4 - 出库单，事务类型TRANSFER
        WmsStockdReceiveSend send2 = new WmsStockdReceiveSend();
        send2.setId("4");
        send2.setBillNo("CK202412280002");
        send2.setBillType("2"); // 出库
        send2.setItemCode("MAT004");
        send2.setItemName("测试物料004");
        send2.setQuantity(75.0);
        send2.setBatchCode("BATCH004");
        send2.setWarehouseCode("WH002");
        send2.setAccountCode("ACC001");
        send2.setTransactionType("TRANSFER"); // 事务类型4
        send2.setPostDate(new Date());
        send2.setUnitPrice(8.0);
        list.add(send2);

        // 测试数据5 - 不同账套
        WmsStockdReceiveSend receive3 = new WmsStockdReceiveSend();
        receive3.setId("5");
        receive3.setBillNo("RK202412280003");
        receive3.setBillType("1"); // 入库
        receive3.setItemCode("MAT005");
        receive3.setItemName("测试物料005");
        receive3.setQuantity(150.0);
        receive3.setBatchCode("BATCH005");
        receive3.setWarehouseCode("WH001");
        receive3.setAccountCode("ACC002"); // 不同账套
        receive3.setTransactionType("RECEIVE"); // 相同事务类型
        receive3.setPostDate(new Date());
        receive3.setUnitPrice(20.0);
        list.add(receive3);

        return list;
    }

    /**
     * 测试按事务类型分组的逻辑
     */
    @Test
    public void testTransactionTypeGrouping() {
        List<WmsStockdReceiveSend> testList = createDiverseTestData();

        // 模拟分组逻辑
        Map<String, List<WmsStockdReceiveSend>> groupMap = testList.stream()
                .collect(Collectors.groupingBy(item -> {
                    String accountCode = item.getAccountCode() != null ? item.getAccountCode() : "DEFAULT";
                    String billType = item.getBillType() != null ? item.getBillType() : "UNKNOWN";
                    String transactionType = item.getTransactionType() != null ? item.getTransactionType() : "DEFAULT";
                    return accountCode + "-" + billType + "-" + transactionType;
                }));

        System.out.println("=== 按事务类型分组结果 ===");
        System.out.println("总共选择了 " + testList.size() + " 个单据");
        System.out.println("分成了 " + groupMap.size() + " 个组进行SAP同步");
        System.out.println();

        int groupIndex = 1;
        for (Map.Entry<String, List<WmsStockdReceiveSend>> entry : groupMap.entrySet()) {
            String groupKey = entry.getKey();
            List<WmsStockdReceiveSend> groupList = entry.getValue();

            String[] keyParts = groupKey.split("-");
            String accountCode = keyParts[0];
            String billType = keyParts[1];
            String transactionType = keyParts.length > 2 ? keyParts[2] : "DEFAULT";

            System.out.println("第 " + groupIndex + " 组:");
            System.out.println("  账套: " + accountCode);
            System.out.println("  单据类型: " + billType + " (" + (billType.equals("1") ? "入库" : "出库") + ")");
            System.out.println("  事务类型: " + transactionType);
            System.out.println("  包含单据数: " + groupList.size());
            System.out.println("  单据号: " + groupList.stream().map(WmsStockdReceiveSend::getBillNo).collect(Collectors.joining(", ")));
            System.out.println();

            groupIndex++;
        }
    }

    /**
     * 测试SAP请求数据结构
     */
    @Test
    public void testSapRequestStructure() {
        List<WmsStockdReceiveSend> testList = createTestData();

        // 模拟构建SAP请求数据
        JSONObject requestJson = buildMockSapRequest(testList);

        System.out.println("SAP请求数据结构:");
        System.out.println(requestJson.toJSONString());

        // 验证Lines结构
        if (requestJson.containsKey("Lines")) {
            System.out.println("\nLines数组内容:");
            requestJson.getJSONArray("Lines").forEach(line -> {
                JSONObject lineObj = (JSONObject) line;
                System.out.println("物料: " + lineObj.getString("ItemCode"));
                System.out.println("数量: " + lineObj.getDouble("Quantity"));
                if (lineObj.containsKey("OBTNs")) {
                    System.out.println("批号信息: " + lineObj.getJSONArray("OBTNs"));
                }
                System.out.println("---");
            });
        }
    }

    /**
     * 模拟构建SAP请求数据
     */
    private JSONObject buildMockSapRequest(List<WmsStockdReceiveSend> receiveSendList) {
        JSONObject requestJson = new JSONObject();
        requestJson.put("DBName", "ACC001");
        requestJson.put("DocDate", "2024-12-28");
        requestJson.put("U_Z98_TYPE", "RECEIVE");
        requestJson.put("U_LYDH", "BATCH_" + System.currentTimeMillis());

        // 构建Lines数组
        List<JSONObject> linesArray = new ArrayList<>();
        for (WmsStockdReceiveSend receiveSend : receiveSendList) {
            JSONObject lineJson = new JSONObject();
            lineJson.put("ItemCode", receiveSend.getItemCode());
            lineJson.put("Quantity", receiveSend.getQuantity());
            lineJson.put("Price", receiveSend.getUnitPrice());
            lineJson.put("WarehouseCode", receiveSend.getWarehouseCode());

            // 构建批次信息
            List<JSONObject> obtnsArray = new ArrayList<>();
            if (receiveSend.getBatchCode() != null && !receiveSend.getBatchCode().isEmpty()) {
                JSONObject obtnJson = new JSONObject();
                obtnJson.put("DistNumber", receiveSend.getBatchCode());
                obtnJson.put("Quantity", receiveSend.getQuantity());
                obtnsArray.add(obtnJson);
            }
            lineJson.put("OBTNs", obtnsArray);

            linesArray.add(lineJson);
        }
        requestJson.put("Lines", linesArray);

        return requestJson;
    }
}
