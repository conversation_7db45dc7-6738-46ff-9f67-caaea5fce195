package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.service.impl.WmsStockdReceiveSendServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 库存收发批量审核功能测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class WmsStockdReceiveSendBatchTest {

    /**
     * 测试批量审核功能的数据结构
     */
    @Test
    public void testBatchAuditDataStructure() {
        // 创建测试数据
        List<WmsStockdReceiveSend> testList = createTestData();
        
        // 验证数据结构
        for (WmsStockdReceiveSend receiveSend : testList) {
            System.out.println("单据号: " + receiveSend.getBillNo());
            System.out.println("物料编号: " + receiveSend.getItemCode());
            System.out.println("数量: " + receiveSend.getQuantity());
            System.out.println("批号: " + receiveSend.getBatchCode());
            System.out.println("单据类型: " + receiveSend.getBillType());
            System.out.println("账套: " + receiveSend.getAccountCode());
            System.out.println("---");
        }
    }

    /**
     * 创建测试数据
     */
    private List<WmsStockdReceiveSend> createTestData() {
        List<WmsStockdReceiveSend> list = new ArrayList<>();
        
        // 测试数据1 - 入库单
        WmsStockdReceiveSend receive1 = new WmsStockdReceiveSend();
        receive1.setId("1");
        receive1.setBillNo("RK202412280001");
        receive1.setBillType("1"); // 入库
        receive1.setItemCode("MAT001");
        receive1.setItemName("测试物料001");
        receive1.setQuantity(100.0);
        receive1.setBatchCode("BATCH001");
        receive1.setWarehouseCode("WH001");
        receive1.setAccountCode("ACC001");
        receive1.setTransactionType("RECEIVE");
        receive1.setPostDate(new Date());
        receive1.setUnitPrice(10.5);
        receive1.setApproveStatus("0");
        receive1.setErpSync("0");
        list.add(receive1);
        
        // 测试数据2 - 出库单
        WmsStockdReceiveSend send1 = new WmsStockdReceiveSend();
        send1.setId("2");
        send1.setBillNo("CK202412280001");
        send1.setBillType("2"); // 出库
        send1.setItemCode("MAT002");
        send1.setItemName("测试物料002");
        send1.setQuantity(50.0);
        send1.setBatchCode("BATCH002");
        send1.setWarehouseCode("WH001");
        send1.setAccountCode("ACC001");
        send1.setTransactionType("SEND");
        send1.setPostDate(new Date());
        send1.setUnitPrice(15.0);
        send1.setApproveStatus("0");
        send1.setErpSync("0");
        list.add(send1);
        
        // 测试数据3 - 同账套不同物料
        WmsStockdReceiveSend receive2 = new WmsStockdReceiveSend();
        receive2.setId("3");
        receive2.setBillNo("RK202412280002");
        receive2.setBillType("1"); // 入库
        receive2.setItemCode("MAT003");
        receive2.setItemName("测试物料003");
        receive2.setQuantity(200.0);
        receive2.setBatchCode("BATCH003");
        receive2.setWarehouseCode("WH002");
        receive2.setAccountCode("ACC001"); // 同一账套
        receive2.setTransactionType("RECEIVE");
        receive2.setPostDate(new Date());
        receive2.setUnitPrice(8.0);
        receive2.setApproveStatus("0");
        receive2.setErpSync("0");
        list.add(receive2);
        
        return list;
    }

    /**
     * 测试SAP请求数据结构
     */
    @Test
    public void testSapRequestStructure() {
        List<WmsStockdReceiveSend> testList = createTestData();
        
        // 模拟构建SAP请求数据
        JSONObject requestJson = buildMockSapRequest(testList);
        
        System.out.println("SAP请求数据结构:");
        System.out.println(requestJson.toJSONString());
        
        // 验证Lines结构
        if (requestJson.containsKey("Lines")) {
            System.out.println("\nLines数组内容:");
            requestJson.getJSONArray("Lines").forEach(line -> {
                JSONObject lineObj = (JSONObject) line;
                System.out.println("物料: " + lineObj.getString("ItemCode"));
                System.out.println("数量: " + lineObj.getDouble("Quantity"));
                if (lineObj.containsKey("OBTNs")) {
                    System.out.println("批号信息: " + lineObj.getJSONArray("OBTNs"));
                }
                System.out.println("---");
            });
        }
    }

    /**
     * 模拟构建SAP请求数据
     */
    private JSONObject buildMockSapRequest(List<WmsStockdReceiveSend> receiveSendList) {
        JSONObject requestJson = new JSONObject();
        requestJson.put("DBName", "ACC001");
        requestJson.put("DocDate", "2024-12-28");
        requestJson.put("U_Z98_TYPE", "RECEIVE");
        requestJson.put("U_LYDH", "BATCH_" + System.currentTimeMillis());
        
        // 构建Lines数组
        List<JSONObject> linesArray = new ArrayList<>();
        for (WmsStockdReceiveSend receiveSend : receiveSendList) {
            JSONObject lineJson = new JSONObject();
            lineJson.put("ItemCode", receiveSend.getItemCode());
            lineJson.put("Quantity", receiveSend.getQuantity());
            lineJson.put("Price", receiveSend.getUnitPrice());
            lineJson.put("WarehouseCode", receiveSend.getWarehouseCode());
            
            // 构建批次信息
            List<JSONObject> obtnsArray = new ArrayList<>();
            if (receiveSend.getBatchCode() != null && !receiveSend.getBatchCode().isEmpty()) {
                JSONObject obtnJson = new JSONObject();
                obtnJson.put("DistNumber", receiveSend.getBatchCode());
                obtnJson.put("Quantity", receiveSend.getQuantity());
                obtnsArray.add(obtnJson);
            }
            lineJson.put("OBTNs", obtnsArray);
            
            linesArray.add(lineJson);
        }
        requestJson.put("Lines", linesArray);
        
        return requestJson;
    }
}
