package org.jeecg.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.exception.SapSyncException;

import java.util.List;

public interface IWmsReceiveSendHandlerService {
    /**
     * 收发单审核后处理：
     * 1. 同步 SAP
     * 2. 如果成功则更新库存 + 单据状态
     * 3. 如果失败则回滚并标记单据状态为同步失败
     *
     * @param wmsStockdReceiveSend 收发单实体
     * @throws SapSyncException 同步SAP失败时抛出
     */
    JSONObject handleAuditData(WmsStockdReceiveSend wmsStockdReceiveSend);

    /**
     * 批量处理收发单审核：
     * 1. 批量处理库存操作
     * 2. 批量同步 SAP
     * 3. 更新单据状态
     *
     * @param receiveSendList 收发单列表
     * @return 处理结果
     */
    JSONObject handleAuditDataBatch(List<WmsStockdReceiveSend> receiveSendList);
}
