package org.jeecg.modules.admin.service;

import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description: WMS库存收发
 * @Author: jeecg-boot
 * @Date:   2024-11-11
 * @Version: V1.0
 */
public interface IWmsStockdReceiveSendService extends IService<WmsStockdReceiveSend> {

    void handleAudit(String ids);
    
    /**
     * 保存库存收发记录并生成物料条码
     * @param wmsStockdReceiveSend 库存收发对象
     * @return 保存后的对象
     */
    WmsStockdReceiveSend saveWithBarcode(WmsStockdReceiveSend wmsStockdReceiveSend);
    
    /**
     * 处理Excel导入数据，为每条记录自动生成唯一物料条码
     * @param receiveList 导入的库存收发记录列表
     * @return 成功导入的记录数
     */
    int importWithBarcode(List<WmsStockdReceiveSend> receiveList);

    void handleAuditBatch(String ids);
}
