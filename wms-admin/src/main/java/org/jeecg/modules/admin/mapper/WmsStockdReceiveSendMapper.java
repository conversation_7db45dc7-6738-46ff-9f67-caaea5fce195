package org.jeecg.modules.admin.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: WMS库存收发
 * @Author: jeecg-boot
 * @Date:   2024-11-11
 * @Version: V1.0
 */
public interface WmsStockdReceiveSendMapper extends BaseMapper<WmsStockdReceiveSend> {

    /**
     * 基于状态的乐观锁更新方法，只有当当前ERP同步状态等于expectedStatus时才更新为newStatus
     * @param id 记录ID
     * @param expectedStatus 期望的当前状态
     * @param newStatus 要更新的新状态
     * @return 更新成功返回true，否则返回false
     */
    @Update("UPDATE wms_stockd_receive_send SET erp_sync = #{newStatus}, update_time = NOW() " +
            "WHERE id = #{id} AND erp_sync = #{expectedStatus}")
    boolean updateWithVersionCheck(@Param("id") String id, 
                                  @Param("expectedStatus") String expectedStatus, 
                                  @Param("newStatus") String newStatus);

    void updateBatchById(List<WmsStockdReceiveSend> receiveSendList);
}
