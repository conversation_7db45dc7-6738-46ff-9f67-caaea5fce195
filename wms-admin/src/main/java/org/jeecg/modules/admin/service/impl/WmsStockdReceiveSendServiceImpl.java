package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.exception.SapSyncException;
import org.jeecg.modules.admin.mapper.WmsStockdReceiveSendMapper;
import org.jeecg.modules.admin.service.IErpSyncService;
import org.jeecg.modules.admin.service.IWmsReceiveSendHandlerService;
import org.jeecg.modules.admin.service.IWmsStockdReceiveSendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

/**
 * @Description: WMS库存收发
 * @Author: jeecg-boot
 * @Date:   2024-11-11
 * @Version: V1.0
 */
@Service
@Slf4j
public class WmsStockdReceiveSendServiceImpl
        extends ServiceImpl<WmsStockdReceiveSendMapper, WmsStockdReceiveSend>
        implements IWmsStockdReceiveSendService {

    @Autowired
    private WmsStockdReceiveSendMapper wmsStockdReceiveSendMapper;

    @Autowired
    private IWmsReceiveSendHandlerService receiveSendHandlerService;
    @Autowired
    private IErpSyncService erpSyncService;

    @Override
    public WmsStockdReceiveSend saveWithBarcode(WmsStockdReceiveSend wmsStockdReceiveSend) {
        // 生成物料条码：年月日时分秒格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String barcode = sdf.format(new Date());

        // 设置物料条码
        wmsStockdReceiveSend.setItemBarcode(barcode);

        // 保存记录
        this.save(wmsStockdReceiveSend);

        return wmsStockdReceiveSend;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int importWithBarcode(List<WmsStockdReceiveSend> receiveList) {
        if (receiveList == null || receiveList.isEmpty()) {
            return 0;
        }

        AtomicInteger successCount = new AtomicInteger(0);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

        // 处理列表中的每条记录
        List<WmsStockdReceiveSend> batchList = new ArrayList<>();

        for (WmsStockdReceiveSend item : receiveList) {
            try {
                // 设置审核状态为未审核
                item.setApproveStatus("0");

                // 生成唯一物料条码：年月日时分秒+三位随机数
                String timestamp = sdf.format(new Date());
                // 添加毫秒级别的时间戳和记录位置偏移，确保唯一性
                String randomSuffix = String.format("%03d", (int)(Math.random() * 1000));
                String uniqueBarcode = timestamp + randomSuffix;

                // 睡眠几毫秒，确保时间戳不同
                try {
                    Thread.sleep(5);
                } catch (InterruptedException e) {
                    log.error("Thread sleep interrupted", e);
                }

                // 设置物料条码
                item.setItemBarcode(uniqueBarcode);

                // 添加到批处理列表
                batchList.add(item);

                // 每100条批量保存一次
                if (batchList.size() >= 100) {
                    saveBatch(batchList);
                    successCount.addAndGet(batchList.size());
                    batchList.clear();
                }
            } catch (Exception e) {
                log.error("导入物料记录异常: {}", e.getMessage(), e);
            }
        }

        // 保存剩余的记录
        if (!batchList.isEmpty()) {
            saveBatch(batchList);
            successCount.addAndGet(batchList.size());
        }

        return successCount.get();
    }

    @Override
    public void handleAudit(String ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        List<String> errorMessages = new ArrayList<>();  // 收集所有错误信息
        Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .map(this::getById)  // 查询记录
                .filter(Objects::nonNull)
                // 只处理状态为非审核通过或同步失败的记录，避免重复同步
                .filter(stockReceiveSend -> {
                    // 如果已经审核通过且同步成功，直接跳过
                    if ("1".equals(stockReceiveSend.getApproveStatus()) &&
                        WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue().equals(stockReceiveSend.getErpSync())) {
                        log.info("单据 [{}] 已审核通过且同步成功，跳过处理", stockReceiveSend.getBillNo());
                        return false;
                    }
                    return true;
                })
                .forEach(stockReceiveSend -> {
                    try {
                        // 设置同步状态为审核中，作为乐观锁的方式防止并发处理
                        String originalSyncStatus = stockReceiveSend.getErpSync();
                        stockReceiveSend.setErpSync(WmsConstant.ErpSyncEnum.AUDITING.getValue());

                        // 使用乐观锁更新，只有当状态与查询时一致才更新
                        boolean updateSuccess = wmsStockdReceiveSendMapper.updateWithVersionCheck(
                            stockReceiveSend.getId(),
                            originalSyncStatus,
                            WmsConstant.ErpSyncEnum.AUDITING.getValue());

                        if (!updateSuccess) {
                            // 如果更新失败，说明记录已被其他线程处理，跳过此记录
                            log.warn("单据 [{}] 正在被其他操作处理，跳过当前审核", stockReceiveSend.getBillNo());
                            return;
                        }

                        // 执行审核操作
                        JSONObject jsonObject = auditSingle(stockReceiveSend);// 审核单条记录
                        if(!jsonObject.getBoolean("result")){
                            errorMessages.add(jsonObject.getString("msg"));
                        }
                    } catch (Exception e) {
                        // 捕获异常，记录错误信息，不影响其他记录
                        String errorMsg = "处理库存收发单据 [" + stockReceiveSend.getBillNo() + "] 失败: " + e.getMessage();
                        errorMessages.add(errorMsg);
                        log.error(errorMsg, e);
                    }
                });

        if (!errorMessages.isEmpty()) {
            // 如果有错误，抛出异常通知前端
            throw new RuntimeException("审核失败，错误信息: " + String.join(", ", errorMessages));
        }
    }

    /**
     * 审核单条记录
     */
    private JSONObject auditSingle(WmsStockdReceiveSend record) {
        // 调用处理服务，包含同步和库存逻辑
        JSONObject jsonObject = receiveSendHandlerService.handleAuditData(record);
        // 更新记录状态
        wmsStockdReceiveSendMapper.updateById(record);
        return jsonObject;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleAuditBatch(String ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }

        // 1. 根据ID获取待处理的数据
        QueryWrapper<WmsStockdReceiveSend> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", Arrays.asList(ids.split(",")));
        List<WmsStockdReceiveSend> receiveSendList = wmsStockdReceiveSendMapper.selectList(queryWrapper);

        if (receiveSendList == null || receiveSendList.isEmpty()) {
            log.warn("根据IDs [{}] 未找到任何库存收发记录", ids);
            return;
        }

        // 2. 过滤已经处理成功的记录，避免重复处理
        List<WmsStockdReceiveSend> needProcessList = receiveSendList.stream()
                .filter(receiveSend -> {
                    // 如果已经审核通过且同步成功，直接跳过
                    if ("1".equals(receiveSend.getApproveStatus()) &&
                        WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue().equals(receiveSend.getErpSync())) {
                        log.info("单据 [{}] 已审核通过且同步成功，跳过处理", receiveSend.getBillNo());
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        if (needProcessList.isEmpty()) {
            log.info("所有单据都已处理完成，无需重复处理");
            return;
        }

        // 3. 调用批量处理服务
        try {
            JSONObject result = receiveSendHandlerService.handleAuditDataBatch(needProcessList);

            // 4. 批量更新数据库记录状态
            for (WmsStockdReceiveSend receiveSend : needProcessList) {
                wmsStockdReceiveSendMapper.updateById(receiveSend);
            }

            // 5. 检查处理结果
            if (!result.getBoolean("result")) {
                String errorMsg = result.getString("msg");
                log.error("批量审核处理失败: {}", errorMsg);
                throw new RuntimeException("批量审核失败: " + errorMsg);
            }

            log.info("批量审核处理成功，共处理 {} 条记录", needProcessList.size());

        } catch (Exception e) {
            log.error("批量审核处理异常", e);
            throw new RuntimeException("批量审核处理异常: " + e.getMessage(), e);
        }
    }
}
