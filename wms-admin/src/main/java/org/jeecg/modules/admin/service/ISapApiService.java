package org.jeecg.modules.admin.service;


import com.alibaba.fastjson.JSONObject;
import org.jeecg.modules.admin.entity.WmsStockDump;
import org.jeecg.modules.admin.entity.WmsStockdReceiveSend;
import org.jeecg.modules.admin.exception.SapSyncException;
import org.jeecg.modules.admin.vo.*;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: Sap接口
 * @Date: 2024-06-03 10:49
 * @Version: V1.0
 */
@Service
public interface ISapApiService  {

    //获取物料信息
    void getItem();
    //获取仓库信息
    void getWarehouse();
    //获取业务伙伴信息
    void getBusinessPartner();
    //获取采购订单
    void getPurchaseOrder();
    //获取销售交货单草稿
    void getSalesdraft();
    //获取采购退货单草稿
    void getPurreturnDraft();
    //获取销售退货单草稿
    void getSalereturnDraft();
    //获取检验记录
    void getInspection();
    //采购收货单同步
    void syncReceive();
    //采购退货单同步
    void syncPurreturn();
    //销售交货单同步
    void syncSales();
    //销售退货单同步
    void syncSalereturn();
    //报工领料同步
    void syncBackflushSend();
    //生产收货单对接
    void syncProduceReceive();
    //生产退料单对接
    void syncProduceReturn();
    //库存收货单对接
    void syncStockReceive();
    //库存发货单对接
    void syncStockSend();
    //生产发料单对接
    void syncProduceSend();
    //库存转储单对接
    void syncStockTransfer(WmsStockDump stockDump) throws SapSyncException;
    //库存转出单批量同步
    void batchSyncStockTransfer(List<WmsStockDump> stockDumpList) throws SapSyncException;
    //倒冲差异单对接
    void syncBackflush();
    /**
     * 根据收发单的类型 (billType) 调用相应 SAP 接口。
     * @param receiveSend  收发单实体
     * @return true 同步成功，false 同步失败
     * @throws SapSyncException 如果 SAP 返回明确的错误信息，可抛出该异常
     */
    JSONObject syncStockReceiveOrSend(WmsStockdReceiveSend receiveSend) throws SapSyncException;

    /**
     * 批量同步库存收发单到SAP
     * @param receiveSendList 库存收发单列表
     * @return 同步结果
     * @throws SapSyncException 同步异常
     */
    JSONObject batchSyncStockReceiveOrSend(List<WmsStockdReceiveSend> receiveSendList) throws SapSyncException;

    //查询全部同步错误的单据
    void getSyncError();

    /**
     * 手动同步指定类型的单据
     * @param billType 单据类型
     * @param ids 单据ID列表
     * @return 同步结果
     * @throws SapSyncException 同步异常
     */
    JSONObject manualSync(String billType, String[] ids) throws SapSyncException;

    /**
     * 手动同步采购入库单
     * @param ids 采购入库单ID列表
     * @return 同步结果
     * @throws SapSyncException 同步异常
     */
    JSONObject manualSyncReceive(String[] ids) throws SapSyncException;
}
