package org.jeecg.modules.admin.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.dto.LogDTO;
import org.jeecg.common.api.dto.message.MessageDTO;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.ComboModel;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.IpUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.admin.constant.WmsConstant;
import org.jeecg.modules.admin.entity.*;
import org.jeecg.modules.admin.exception.SapSyncException;
import org.jeecg.modules.admin.mapper.WmsStockDumpMapper;
import org.jeecg.modules.admin.mapper.WmsStockdReceiveSendMapper;
import org.jeecg.modules.admin.service.*;
import org.jeecg.modules.admin.util.SapApiUtil;
import org.jeecg.modules.base.service.BaseCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.stream.IntStream;
import java.util.concurrent.TimeUnit;

@Service
public class SapApiServiceImpl implements ISapApiService {

    private static final Logger log = LoggerFactory.getLogger(SapApiServiceImpl.class);
    @Value("${sap.api.main-base-url}")
    private String mainBaseUrl;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private IWmsSpecMatchItemService wmsSpecMatchItemService;
    @Autowired
    private IWmsWarehouseService wmsWarehouseService;
    @Autowired
    private BaseCommonService baseCommonService;
    @Autowired
    private ISysBaseAPI sysBaseAPI;
    @Autowired
    private IWmsBusinessPartnerService wmsBusinessPartnerService;
    @Autowired
    private IWmsPurchaseService wmsPurchaseService;
    @Autowired
    private IWmsSalesdraftService wmsSalesDraftService;
    @Autowired
    private IWmsPurreturnDraftService wmsPurReturnDraftService;
    @Autowired
    private IWmsSalereturnDraftService wmsSaleReturnDraftService;
    @Autowired
    private IWmsInspectService wmsInspectService;
    @Autowired
    private IWmsInspectdetailService wmsInspectDetailService;
    @Autowired
    private IWmsReceiveService wmsReceiveService;
    @Autowired
    private IWmsReceivedetailService wmsReceivedetailService;
    @Autowired
    private IWmsPurchaseReturnService wmsPurchaseReturnService;
    @Autowired
    private IWmsSendService wmsSendService;
    @Autowired
    private IWmsSenddetailService wmsSendDetailService;
    @Autowired
    private IWmsSaleReturnService wmsSaleReturnService;
    @Autowired
    private WmsStockDumpMapper wmsStockDumpMapper;
    @Autowired
    private IWmsConttaskHisService wmsConttaskHisService;
    @Autowired
    private IWmsStockdetailService wmsStockdetailService;
    @Autowired
    private IWmsPurchaseDetailService wmsPurchaseDetailService;
    @Autowired
    private WmsStockdReceiveSendMapper wmsStockdReceiveSendMapper;
    @Autowired
    private IWmsPurchaseReturnDetailService wmsPurchaseReturnDetailService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private RedisUtil redisUtil;


    // 物料接口路径和Redis键名
    private static final String ENDPOINT_GET_ITEM = "/MD/GetOITM";
    private static final String REDIS_KEY_START_TIME_ITEM = "sap_api_start_time_item";
    // 仓库接口路径和Redis键名
    private static final String ENDPOINT_GET_WAREHOUSE = "/MD/GetOWHS";
    private static final String REDIS_KEY_START_TIME_WAREHOUSE = "sap_api_start_time_warehouse";
    //业务伙伴接口路径和Redis键名
    private static final String ENDPOINT_GET_BUSINESS_PARTNER = "/MD/GetOCRD";
    private static final String REDIS_KEY_START_TIME_BUSINESS_PARTNER = "sap_api_start_time_business_partner";
    // 采购订单接口路径和Redis键名
    private static final String ENDPOINT_GET_PURCHASE_ORDER = "/Doc/GetOPOR";
    private static final String REDIS_KEY_START_TIME_PURCHASE_ORDER = "sap_api_start_time_purchase_order";
    //销售交货草稿接口路径和Redis键名
    private static final String ENDPOINT_GET_SALES_DRAFT = "/Doc/GetOPDNDraft";
    private static final String REDIS_KEY_START_TIME_SALES_DRAFT = "sap_api_start_time_sales_draft";
    //采购退货草稿接口路径和Redis键名
    private static final String ENDPOINT_GET_PURRETURN_DRAFT = "/Doc/GetORPNDraft";
    private static final String REDIS_KEY_START_TIME_PURRETURN_DRAFT = "sap_api_start_time_purreturn_draft";
    //销售退货单草稿接口路径和Redis键名
    private static final String ENDPOINT_GET_SALERETURN_DRAFT = "/Doc/GetORDNDraft";
    private static final String REDIS_KEY_START_TIME_SALERETURN_DRAFT = "sap_api_start_time_salesreturn_draft";
    //检验记录接口路径和Redis键名
    private static final String ENDPOINT_GET_INSPECTION = "/Doc/GetZ12PRBN";
    private static final String REDIS_KEY_START_TIME_INSPECTION = "sap_api_start_time_inspection";
    //采购收货单接口路径
    private static final String ENDPOINT_GET_PURCHASE_RECEIVE = "/Doc/SyncOPDN";
    //采购退货单接口路径
    private static final String ENDPOINT_GET_PURCHASE_RETURN = "/Doc/SyncORPD";
    //采购交货单接口路径
    private static final String ENDPOINT_GET_PURCHASE_DELIVERY = "/Doc/SyncODLN";
    //销售退货单接口路径
    private static final String ENDPOINT_GET_SALES_RETURN = "/Doc/SyncORDN";
    //生产收货单接口路径
    private static final String ENDPOINT_GET_PRODUCTION_RECEIVE = "/Doc/SyncOIGNFromOWOR";
    //生产退料单接口路径
    private static final String ENDPOINT_GET_PRODUCTION_RETURN = "/Doc/SyncOIGNFromOWOR";
    //生产发料单接口路径
    private static final String ENDPOINT_GET_PRODUCTION_DELIVERY = "/Doc/SyncOIGEFromOWOR";
    //库存转储单接口路径
    private static final String ENDPOINT_GET_STOCK_TRANSFER = "/Doc/SyncOWTR";
    //库存收货单接口路径
    private static final String ENDPOINT_GET_STOCK_RECEIVE = "/Doc/SyncOIGN";
    //库存发货单接口路径
    private static final String ENDPOINT_GET_STOCK_SEND = "/Doc/SyncOIGE";
    //倒冲料差异接口路径
    private static final String ENDPOINT_GET_BACK_FLUSH_DIFF = "/Doc/SyncBackFlushDiff";
    // 定义日期格式(年-月-日 时:分:秒)
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    //定义日期格式(年-月-日)
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    // 定义需要查询的DBName列表
    private static final String[] DB_NAMES = {"ZXKJ_ZSK", "JS_XK"};

//    private static final String[] DB_NAMES ={"ZXKLZK"};
    /**
     * 通用的请求方法，减少代码重复
     *
     * @param endpoint 接口路径
     * @param redisKey Redis键名
     * @return 请求结果
     */
    private JSONObject getDataFromApi(String endpoint, String redisKey, String name, String dbName) {
        return SapApiUtil.getDataFromApi(mainBaseUrl, endpoint, redisKey, name, dbName, stringRedisTemplate, baseCommonService);
    }

    public JSONObject executeRequest(String url, JSONObject requestObject, String name) {
        return SapApiUtil.executeRequest(url, requestObject, name, baseCommonService);
    }

    //获取本机ip地址
    private String GetLocalIpAddress() {
        return SapApiUtil.getLocalIpAddress();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getItem() {
        String redisKey = REDIS_KEY_START_TIME_ITEM;
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_ITEM, redisKey, "物料信息同步", dbName);

            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = jsonObject.getString("sign"); // 获取Sign作为accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    // 没有数据，不更新 Redis 中的时间
                    log.info("没有需要同步的物料数据。");
                    continue;
                }

                try {
                    // 提取所有的 ItemCode
                    List<String> itemCodeList = new ArrayList<>();
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject item = dataArray.getJSONObject(i);
                        itemCodeList.add(item.getString("ItemCode"));
                    }

                    // 批量查询已有的记录，增加 account_code 条件
                    List<WmsSpecMatchItem> existingItems = wmsSpecMatchItemService.list(new QueryWrapper<WmsSpecMatchItem>().in("item_code", itemCodeList).eq("account_code", accountCode));

                    // 将已有的记录放入 Map，方便查找
                    Map<String, WmsSpecMatchItem> existingItemMap = existingItems.stream().collect(Collectors.toMap(WmsSpecMatchItem::getItemCode, Function.identity()));

                    List<WmsSpecMatchItem> itemsToUpdate = new ArrayList<>();
                    List<WmsSpecMatchItem> itemsToInsert = new ArrayList<>();

                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject item = dataArray.getJSONObject(i);
                        WmsSpecMatchItem wmsSpecMatchItem = new WmsSpecMatchItem();

                        // 设置属性，注意字段名称的对应关系
                        wmsSpecMatchItem.setItemCode(item.getString("ItemCode"));
                        wmsSpecMatchItem.setItemName(item.getString("ItemName"));
                        wmsSpecMatchItem.setItemNameShort(item.getString("FrgnName")); // 物料简称
                        wmsSpecMatchItem.setSpecCode(item.getString("U_Z98_GUIG")); // 规格代码
                        wmsSpecMatchItem.setItemUnit(item.getString("InvntryUom")); // 规格名称（库存单位）
                        wmsSpecMatchItem.setWarehouseCode(item.getString("DfltWH")); // 默认仓库编号
                        wmsSpecMatchItem.setDeliveryMethod(item.getString("IssueMthd")); // 发货方法
                        wmsSpecMatchItem.setManageMethod(item.getString("ManBtchNum")); // 管理方法
                        wmsSpecMatchItem.setGroupCode(item.getString("ItmsGrpCod")); // 物料组代码
                        wmsSpecMatchItem.setGroupName(item.getString("ItmsGrpNam")); // 物料组名称
                        // 采购标记
                        wmsSpecMatchItem.setPurchaseFlag(convertYnToInteger(item.getString("PrchseItem")));
                        // 销售标记
                        wmsSpecMatchItem.setSaleFlag(convertYnToInteger(item.getString("SellItem")));
                        // 库存标记
                        wmsSpecMatchItem.setWarehouseFlag(convertYnToInteger(item.getString("InvntItem")));

                        // 是否来自ERP
                        wmsSpecMatchItem.setFromErp("1"); // "1"表示来自ERP，"0"表示不来自ERP
                        wmsSpecMatchItem.setAccountCode(accountCode); // 设置账套信息

                        // 设置物料类型（根据业务规则，这里假设使用物料组名称）
                        wmsSpecMatchItem.setMaterialType(item.getString("ItmsGrpNam"));
                        wmsSpecMatchItem.setPrice(item.getDouble("AvgPrice")); // 平均价格
                        wmsSpecMatchItem.setShelfLife(item.getDouble("U_T100")); // 保质期

                        // 解析创建时间和更新时间
                        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
                        try {
                            String createDateStr = item.getString("CreateDate");
                            String updateDateStr = item.getString("UpdateDate");
                            if (createDateStr != null) {
                                LocalDateTime createDate = LocalDateTime.parse(createDateStr, formatter);
                                wmsSpecMatchItem.setCreateTime(Date.from(createDate.atZone(ZoneId.systemDefault()).toInstant()));
                            }
                            if (updateDateStr != null) {
                                LocalDateTime updateDate = LocalDateTime.parse(updateDateStr, formatter);
                                wmsSpecMatchItem.setUpdateTime(Date.from(updateDate.atZone(ZoneId.systemDefault()).toInstant()));
                            }
                        } catch (Exception e) {
                            // 日期解析错误
                        }

                        // 设置是否有效
                        wmsSpecMatchItem.setEnableFlag("Y".equals(item.getString("validFor")) ? "1" : "0");

                        // 设置备注
                        wmsSpecMatchItem.setRemark(item.getString("UserText"));

                        // 设置免检标记（假设 U_Z12_IQC 表示是否需要检验）
                        wmsSpecMatchItem.setFreeFlag("Y".equals(item.getString("U_Z12_IQC")) ? "0" : "1"); // "0"表示需要检验，"1"表示免检

                        // 检查是否存在
                        WmsSpecMatchItem existingItem = existingItemMap.get(wmsSpecMatchItem.getItemCode());
                        if (existingItem != null) {
                            // 更新
                            wmsSpecMatchItem.setId(existingItem.getId());
                            itemsToUpdate.add(wmsSpecMatchItem);
                        } else {
                            // 插入
                            itemsToInsert.add(wmsSpecMatchItem);
                        }
                    }

                    // 批量更新和插入
                    if (!itemsToUpdate.isEmpty()) {
                        wmsSpecMatchItemService.updateBatchById(itemsToUpdate);
                    }
                    if (!itemsToInsert.isEmpty()) {
                        wmsSpecMatchItemService.saveBatch(itemsToInsert);
                    }

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    // 同步出现异常，打印错误信息，不更新 Redis 中的时间
                    e.printStackTrace();
                    log.error("物料数据同步出现异常：{}", e.getMessage());
                }
            } else {
                // 接口调用失败，错误信息
                log.error("物料接口调用失败，错误信息：{}", jsonObject.getString("msg"));
                // 请求失败，不更新 Redis 中的时间
            }
        }
    }

    private String convertYnToInteger(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null; // 或者你可以选择返回 0，取决于需求
        }
        return "Y".equalsIgnoreCase(value) ? "1" : "N".equalsIgnoreCase(value) ? "0" : null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getWarehouse() {
        String redisKey = REDIS_KEY_START_TIME_WAREHOUSE;
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_WAREHOUSE, redisKey, "仓库信息同步", dbName);

            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = jsonObject.getString("sign"); // 获取 Sign 作为 accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    // 没有数据，不更新 Redis 中的时间
                    log.info("没有需要同步的仓库数据。");
                    continue;
                }

                try {

                    // 提取所有的 warehouseCode
                    List<String> warehouseCodeList = new ArrayList<>();
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject item = dataArray.getJSONObject(i);
                        warehouseCodeList.add(item.getString("WhsCode"));
                    }

                    // 批量查询已有的记录，增加 account_code 条件
                    List<WmsWarehouse> existingWarehouses = wmsWarehouseService.list(new QueryWrapper<WmsWarehouse>().in("warehouse_code", warehouseCodeList).eq("account_code", accountCode));

                    // 将已有的记录放入 Map，方便查找
                    Map<String, WmsWarehouse> existingWarehouseMap = existingWarehouses.stream().collect(Collectors.toMap(WmsWarehouse::getWarehouseCode, Function.identity()));

                    List<WmsWarehouse> warehousesToUpdate = new ArrayList<>();
                    List<WmsWarehouse> warehousesToInsert = new ArrayList<>();

                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject item = dataArray.getJSONObject(i);
                        WmsWarehouse wmsWarehouse = new WmsWarehouse();

                        // 设置属性，注意字段名称的对应关系
                        wmsWarehouse.setWarehouseCode(item.getString("WhsCode"));
                        wmsWarehouse.setWarehouseName(item.getString("WhsName"));
                        wmsWarehouse.setAccountCode(accountCode); // 设置账套信息

                        // 解析创建时间和更新时间
                        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
                        try {
                            String createDateStr = item.getString("CreateDate");
                            String updateDateStr = item.getString("UpdateDate");
                            if (createDateStr != null) {
                                LocalDateTime createDate = LocalDateTime.parse(createDateStr, formatter);
                                wmsWarehouse.setCreateTime(Date.from(createDate.atZone(ZoneId.systemDefault()).toInstant()));
                            }
                            if (updateDateStr != null) {
                                LocalDateTime updateDate = LocalDateTime.parse(updateDateStr, formatter);
                                wmsWarehouse.setUpdateTime(Date.from(updateDate.atZone(ZoneId.systemDefault()).toInstant()));
                            }
                        } catch (Exception e) {
                            // 日期解析错误
                        }

                        // 设置是否有效
                        wmsWarehouse.setEnableFlag("1");

                        // 检查是否存在
                        WmsWarehouse existingWarehouse = existingWarehouseMap.get(wmsWarehouse.getWarehouseCode());
                        if (existingWarehouse != null) {
                            // 更新
                            wmsWarehouse.setId(existingWarehouse.getId());
                            warehousesToUpdate.add(wmsWarehouse);
                        } else {
                            // 插入
                            warehousesToInsert.add(wmsWarehouse);
                        }
                    }

                    // 批量更新和插入
                    if (!warehousesToUpdate.isEmpty()) {
                        wmsWarehouseService.updateBatchById(warehousesToUpdate);
                    }
                    if (!warehousesToInsert.isEmpty()) {
                        wmsWarehouseService.saveBatch(warehousesToInsert);
                    }

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    // 同步出现异常，打印错误信息，不更新 Redis 中的时间
                    e.printStackTrace();
                    log.error("仓库数据同步出现异常：{}", e.getMessage());
                }
            } else {
                // 接口调用失败，错误信息
                log.error("仓库接口调用失败，错误信息：{}", jsonObject.getString("msg"));
                // 请求失败，不更新 Redis 中的时间
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getBusinessPartner() {
        String redisKey = REDIS_KEY_START_TIME_BUSINESS_PARTNER;
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_BUSINESS_PARTNER, redisKey, "业务伙伴信息同步", dbName);

            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = jsonObject.getString("sign"); // 获取 Sign 作为 accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    // 没有数据，不更新 Redis 中的时间
                    log.info("没有需要同步的业务伙伴数据。");
                    continue;
                }

                try {
                    // 调用处理方法，传递 accountCode

                    // 提取所有的 partnerCode
                    List<String> partnerCodeList = new ArrayList<>();
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject item = dataArray.getJSONObject(i);
                        partnerCodeList.add(item.getString("CardCode"));
                    }

                    // 批量查询已有的记录，增加 account_code 条件
                    List<WmsBusinessPartner> existingPartners = wmsBusinessPartnerService.list(new QueryWrapper<WmsBusinessPartner>().in("partner_code", partnerCodeList).eq("account_code", accountCode));

                    // 将已有的记录放入 Map，方便查找
                    Map<String, WmsBusinessPartner> existingPartnerMap = existingPartners.stream().collect(Collectors.toMap(WmsBusinessPartner::getPartnerCode, Function.identity()));

                    List<WmsBusinessPartner> partnersToUpdate = new ArrayList<>();
                    List<WmsBusinessPartner> partnersToInsert = new ArrayList<>();

                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject item = dataArray.getJSONObject(i);
                        WmsBusinessPartner partner = new WmsBusinessPartner();

                        // 设置属性，注意字段名称的对应关系
                        partner.setPartnerCode(item.getString("CardCode")); // 业务伙伴代码
                        partner.setPartnerName(item.getString("CardName")); // 业务伙伴名称
                        partner.setEnglishName(item.getString("CardFName")); // 外文名称（简称）
                        partner.setPartnerType(item.getString("CardType")); // 业务伙伴类型
                        partner.setAccountCode(accountCode); // 设置账套信息

                        // 解析创建时间和更新时间
                        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
                        try {
                            String createDateStr = item.getString("CreateDate");
                            String updateDateStr = item.getString("UpdateDate");
                            if (createDateStr != null) {
                                LocalDateTime createDate = LocalDateTime.parse(createDateStr, formatter);
                                partner.setCreateTime(Date.from(createDate.atZone(ZoneId.systemDefault()).toInstant()));
                            }
                            if (updateDateStr != null) {
                                LocalDateTime updateDate = LocalDateTime.parse(updateDateStr, formatter);
                                partner.setUpdateTime(Date.from(updateDate.atZone(ZoneId.systemDefault()).toInstant()));
                            }
                        } catch (Exception e) {
                            // 日期解析错误
                        }

                        // 设置可用标记
                        partner.setEnableFlag(item.getString("validFor"));

                        // 检查是否存在
                        WmsBusinessPartner existingPartner = existingPartnerMap.get(partner.getPartnerCode());
                        if (existingPartner != null) {
                            // 更新
                            partner.setId(existingPartner.getId());
                            partnersToUpdate.add(partner);
                        } else {
                            // 插入
                            partnersToInsert.add(partner);
                        }
                    }

                    // 批量更新和插入
                    if (!partnersToUpdate.isEmpty()) {
                        wmsBusinessPartnerService.updateBatchById(partnersToUpdate);
                    }
                    if (!partnersToInsert.isEmpty()) {
                        wmsBusinessPartnerService.saveBatch(partnersToInsert);
                    }

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    // 同步出现异常，打印错误信息，不更新 Redis 中的时间
                    e.printStackTrace();
                    log.error("业务伙伴数据同步出现异常：{}", e.getMessage());
                }
            } else {
                // 接口调用失败，错误信息
                log.error("业务伙伴接口调用失败，错误信息：{}", jsonObject.getString("msg"));
                // 请求失败，不更新 Redis 中的时间
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getPurchaseOrder() {
        String redisKey = REDIS_KEY_START_TIME_PURCHASE_ORDER;
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_PURCHASE_ORDER, redisKey, "采购订单同步", dbName);

            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = jsonObject.getString("sign"); // 获取Sign作为accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    // 没有数据，不更新 Redis 中的时间
                    log.info("没有需要同步的采购订单数据。");
                    continue;
                }

                try {
                    // 调用处理方法，传递 accountCode
                    processPurchaseOrderData(dataArray, accountCode);

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    // 同步出现异常，打印错误信息，不更新 Redis 中的时间
                    e.printStackTrace();
                    log.error("采购订单数据同步出现异常：{}", e.getMessage());
                }
            } else {
                // 接口调用失败，错误信息
                log.error("采购订单接口调用失败，错误信息：{}", jsonObject.getString("msg"));
                // 请求失败，不更新 Redis 中的时间
            }
        }
    }

    private void processPurchaseOrderData(JSONArray dataArray, String accountCode) {
        try {
            // 提取所有的采购订单编号（DocEntry）并转换为 List<String>
            List<String> serialNumberList = IntStream.range(0, dataArray.size()).mapToObj(dataArray::getJSONObject).map(item -> item.getString("DocEntry")).collect(Collectors.toList());

            // 批量查询已有的采购订单主表记录
            List<WmsPurchase> existingPurchases = wmsPurchaseService.list(new QueryWrapper<WmsPurchase>().in("serial_number", serialNumberList).eq("account_code", accountCode));

            // 将已有的记录放入 Map，使用 serialNumber + workNo 作为复合键，方便查找
            Map<String, WmsPurchase> existingPurchaseMap = existingPurchases.stream().collect(Collectors.toMap(purchase -> generateCompositeKey(purchase.getSerialNumber(), purchase.getWorkNo()), Function.identity(), (purchase1, purchase2) -> {
                log.warn("Duplicate key found for serialNumber: {}, workNo: {}. Keeping the first occurrence.", purchase1.getSerialNumber(), purchase1.getWorkNo());
                return purchase1;
            }));

            // 遍历数据，处理每个采购订单
            IntStream.range(0, dataArray.size()).forEach(i -> {
                JSONObject item = dataArray.getJSONObject(i);

                // 处理采购订单主表数据（OPOR）
                WmsPurchase wmsPurchase = new WmsPurchase();
                wmsPurchase.setSerialNumber(item.getInteger("DocEntry")); // 单据标识
                wmsPurchase.setWorkNo(item.getString("DocNum")); // 单据号
                wmsPurchase.setBillStatus(item.getString("DocStatus")); // 单据状态
                wmsPurchase.setPostDate(parseDateTime(item.getString("DocDate"))); // 过账日期
                wmsPurchase.setDeliveryDate(parseDateTime(item.getString("DocDueDate"))); // 交货日期
                wmsPurchase.setSupplierCode(item.getString("CardCode")); // 供应商代码
                wmsPurchase.setSupplierName(item.getString("CardName")); // 供应商名称
                wmsPurchase.setUpdateTime(parseDateTime(item.getString("UpdateDate"))); // 更新时间
                wmsPurchase.setCreateTime(parseDateTime(item.getString("CreateDate"))); // 创建时间
                wmsPurchase.setRemark(item.getString("Comments")); // 备注
                wmsPurchase.setAccountCode(accountCode); // 账套代码

                // 生成复合键
                String compositeKey = generateCompositeKey(wmsPurchase.getSerialNumber(), wmsPurchase.getWorkNo());

                // 检查主表记录是否存在
                WmsPurchase existingPurchase = existingPurchaseMap.get(compositeKey);

                // 处理采购订单明细数据（POR1）
                List<WmsPurchaseDetail> purchaseDetailList = Optional.ofNullable(item.getJSONArray("Lines")).map(detailArray -> {
                    List<WmsPurchaseDetail> details = new ArrayList<>();

                    // 如果是更新操作，先获取现有的明细记录
                    Map<String, WmsPurchaseDetail> existingDetailMap = new HashMap<>();
                    if (existingPurchase != null) {
                        List<WmsPurchaseDetail> existingDetails = wmsPurchaseDetailService.selectByMainId(existingPurchase.getId());
                        if (existingDetails != null) {
                            existingDetailMap = existingDetails.stream().collect(Collectors.toMap(detail -> detail.getItemNumber() + "_" + detail.getLineNo(), Function.identity(), (detail1, detail2) -> {
                                log.warn("Duplicate detail key found for itemNumber: {}, lineNo: {}. Keeping the first occurrence.", detail1.getItemNumber(), detail1.getLineNo());
                                return detail1;
                            }));
                        }
                    }

                    for (int j = 0; j < detailArray.size(); j++) {
                        JSONObject detailItem = detailArray.getJSONObject(j);
                        WmsPurchaseDetail wmsPurchaseDetail = new WmsPurchaseDetail();
                        wmsPurchaseDetail.setSerialNumber(wmsPurchase.getSerialNumber()); // 单据标识
                        wmsPurchaseDetail.setWorkNo(wmsPurchase.getWorkNo()); // 单据号
                        wmsPurchaseDetail.setLineNo(detailItem.getInteger("LineNum")); // 行编号
                        wmsPurchaseDetail.setItemNumber(detailItem.getString("ItemCode")); // 物料编号
                        wmsPurchaseDetail.setItemName(detailItem.getString("Dscription")); // 物料描述
                        wmsPurchaseDetail.setBuyQty(detailItem.getDouble("Quantity")); // 数量
                        wmsPurchaseDetail.setWarehouseCode(detailItem.getString("WhsCode")); // 仓库编号

                        if (existingPurchase != null) {
                            String key = wmsPurchaseDetail.getItemNumber() + "_" + wmsPurchaseDetail.getLineNo();
                            WmsPurchaseDetail existingDetail = existingDetailMap.get(key);
                            if (existingDetail != null) {
                                // 保留现有的 printedQty 和 printedNumber
                                wmsPurchaseDetail.setPrintedQty(existingDetail.getPrintedQty());
                                wmsPurchaseDetail.setPrintedNumber(existingDetail.getPrintedNumber());
                                wmsPurchaseDetail.setPrintedTime(existingDetail.getPrintedTime());
                            } else {
                                // 新增的明细，初始化 printedQty 和 printedNumber
                                wmsPurchaseDetail.setPrintedQty(0.0);
                                wmsPurchaseDetail.setPrintedNumber(0);
                                wmsPurchaseDetail.setPrintedTime(null);
                            }
                        } else {
                            // 新增操作，初始化 printedQty 和 printedNumber
                            wmsPurchaseDetail.setPrintedQty(0.0);
                            wmsPurchaseDetail.setPrintedNumber(0);
                            wmsPurchaseDetail.setPrintedTime(null);
                        }

                        details.add(wmsPurchaseDetail);
                    }

                    return details;
                }).orElse(new ArrayList<>());

                // 如果主表已存在，则不做处理
                if (existingPurchase != null) {
                    log.info("采购订单已存在，不做处理。");
                } else {
                    // 插入主表和子表
                    wmsPurchaseService.saveMain(wmsPurchase, purchaseDetailList);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理采购订单数据时发生异常：{}", e.getMessage());
            throw new RuntimeException("处理采购订单数据时发生异常", e);
        }
    }


    // 解析日期时间
    private Date parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
            LocalDateTime localDateTime = LocalDateTime.parse(dateTimeStr, formatter);
            return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void getSalesdraft() {
        String redisKey = REDIS_KEY_START_TIME_SALES_DRAFT; // Redis键
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_SALES_DRAFT, redisKey, "销售交货草稿同步", dbName);

            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = jsonObject.getString("sign"); // 获取Sign作为accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    log.info("没有需要同步的销售交货草稿数据。");
                    continue;
                }

                try {
                    // 调用处理方法，传递 accountCode
                    processSalesDraftData(dataArray, accountCode);

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("销售交货草稿数据同步出现异常：{}", e.getMessage());
                }
            } else {
                log.error("销售交货草稿接口调用失败，错误信息：{}", jsonObject.getString("msg"));
            }
        }
    }

    private void processSalesDraftData(JSONArray dataArray, String accountCode) {
        try {
            // 提取所有 serialNumber (DocEntry) 并转换为 List<String>
            List<String> serialNumberList = IntStream.range(0, dataArray.size()).mapToObj(dataArray::getJSONObject).map(item -> item.getString("DocEntry")).collect(Collectors.toList());

            // 批量查询已有的销售交货草稿记录
            List<WmsSalesdraft> existingDrafts = wmsSalesDraftService.list(new QueryWrapper<WmsSalesdraft>().in("serial_number", serialNumberList).eq("account_code", accountCode));

            // 将已有的记录放入 Map，使用 serialNumber + workNo 作为复合键，方便查找
            Map<String, WmsSalesdraft> existingDraftMap = existingDrafts.stream().collect(Collectors.toMap(draft -> generateCompositeKey(draft.getSerialNumber(), draft.getWorkNo()), Function.identity(), (draft1, draft2) -> {
                log.warn("Duplicate key found for serialNumber: {}, workNo: {}. Keeping the first occurrence.", draft1.getSerialNumber(), draft1.getWorkNo());
                return draft1;
            }));

            // 处理每一条数据
            IntStream.range(0, dataArray.size()).forEach(i -> {
                JSONObject item = dataArray.getJSONObject(i);

                // 处理销售交货草稿主表数据
                WmsSalesdraft wmsSalesDraft = new WmsSalesdraft();
                wmsSalesDraft.setSerialNumber(item.getInteger("DocEntry")); // 单据标识
                wmsSalesDraft.setWorkNo(item.getString("DocNum")); // 单据号
                wmsSalesDraft.setCargoNo(item.getString("U_T016")); // 货号
                wmsSalesDraft.setFromErp(WmsConstant.FromErpEnum.YES.getValue());
                wmsSalesDraft.setPostDate(parseDateTime(item.getString("DocDate"))); // 过账日期
                wmsSalesDraft.setDeliveryDate(parseDateTime(item.getString("DocDueDate"))); // 交货日期
                wmsSalesDraft.setCustomerCode(item.getString("CardCode")); // 客户代码
                wmsSalesDraft.setCustomerName(item.getString("CardName")); // 客户名称
                wmsSalesDraft.setDeliveryType(item.getString("U_T040")); // 交货类型
                wmsSalesDraft.setRemark(item.getString("U_Z98_BAK1")); // 交货备注1
                wmsSalesDraft.setRemark2(item.getString("U_Z98_BAK2")); // 交货备注2
                wmsSalesDraft.setAccountCode(accountCode); // 账套代码
                wmsSalesDraft.setFormalDataFlag(WmsConstant.FormalDataFlagEnum.NO.getValue());

                // 生成复合键
                String compositeKey = generateCompositeKey(wmsSalesDraft.getSerialNumber(), wmsSalesDraft.getWorkNo());

                // 检查主表记录是否存在
                WmsSalesdraft existingDraft = existingDraftMap.get(compositeKey);

                // 处理销售交货草稿明细数据
                List<WmsSalesdraftDetail> draftDetailList = new ArrayList<>();
                JSONArray linesArray = item.getJSONArray("Lines");
                for (int j = 0; j < linesArray.size(); j++) {
                    JSONObject lineItem = linesArray.getJSONObject(j);
                    String itemCode = lineItem.getString("ItemCode");
                    String itemName = lineItem.getString("ItemName");
                    double totalQuantity = lineItem.getDouble("Quantity"); // 总数量

                    // 处理批次信息
                    JSONArray batchDetailsArray = lineItem.getJSONArray("BatchDetails");
                    for (int k = 0; k < batchDetailsArray.size(); k++) {
                        JSONObject batchDetail = batchDetailsArray.getJSONObject(k);
                        WmsSalesdraftDetail wmsSalesDraftDetail = new WmsSalesdraftDetail();
                        wmsSalesDraftDetail.setSerialNumber(wmsSalesDraft.getSerialNumber()); // 单据标识
                        wmsSalesDraftDetail.setWorkNo(wmsSalesDraft.getWorkNo()); // 使用 WorkNo 而非 serialNumber
                        wmsSalesDraftDetail.setLineNo(lineItem.getInteger("LineNum")); // 行编号
                        wmsSalesDraftDetail.setItemNumber(itemCode); // 物料编号
                        wmsSalesDraftDetail.setItemName(itemName); // 物料描述
                        wmsSalesDraftDetail.setPlanQty(batchDetail.getDouble("Quantity")); // 交货数量
                        wmsSalesDraftDetail.setAllQty(totalQuantity); // 总数量
                        wmsSalesDraftDetail.setBatchCode(batchDetail.getString("DistNumber")); // 批次号
                        wmsSalesDraftDetail.setItemSpec(lineItem.getString("U_Z98_GUIG")); // 规格
                        wmsSalesDraftDetail.setItemUnit(lineItem.getString("InvntryUom")); // 库存计量单位
                        wmsSalesDraftDetail.setWarehouseCode(lineItem.getString("WhsCode")); // 仓库编号
                        wmsSalesDraftDetail.setWarehouseName(getWarehouseName(lineItem.getString("WhsCode"), accountCode)); // 仓库名称
                        wmsSalesDraftDetail.setDeliveryTime(parseDateTime(lineItem.getString("U_L008")));
                        wmsSalesDraftDetail.setUnitWeight(lineItem.getString("U_Z98_GUIG")); // 单个重量
                        wmsSalesDraftDetail.setTotalWeight(lineItem.getBigDecimal("U_L001")); // 总重量
                        draftDetailList.add(wmsSalesDraftDetail);
                    }
                }

                // 如果主表已存在，则不做处理
                if (existingDraft != null) {
                    if ("1".equals(existingDraft.getFormalDataFlag())) {
                        // formalDataFlag 为 "1"，不做任何操作
                        log.info("销售交货草稿已生成，formalDataFlag为1，不做处理。SerialNumber: {}, WorkNo: {}", existingDraft.getSerialNumber(), existingDraft.getWorkNo());
                    } else {
                        wmsSalesDraft.setId(existingDraft.getId());
                        // formalDataFlag 不为 "1"，更新该条记录
                        log.info("更新销售交货草稿记录。SerialNumber: {}, WorkNo: {}", wmsSalesDraft.getSerialNumber(), wmsSalesDraft.getWorkNo());
                        wmsSalesDraftService.updateMain(wmsSalesDraft, draftDetailList);
                    }
                } else {
                    // 插入主表和子表
                    log.info("插入新的销售交货草稿记录。SerialNumber: {}, WorkNo: {}", wmsSalesDraft.getSerialNumber(), wmsSalesDraft.getWorkNo());
                    wmsSalesDraftService.saveMain(wmsSalesDraft, draftDetailList);
                }

            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理销售交货草稿数据时发生异常：{}", e.getMessage());
            throw new RuntimeException("处理销售交货草稿数据时发生异常", e);
        }
    }

    /**
     * 生成复合键，确保唯一性。
     *
     * @param serialNumber 单据标识
     * @param workNo       单据号
     * @return 复合键字符串
     */
    private String generateCompositeKey(Integer serialNumber, String workNo) {
        return serialNumber + "_" + workNo;
    }

    //根据仓库编号和账套信息查询仓库名称
    private String getWarehouseName(String warehouseCode, String accountCode) {
        WmsWarehouse wmsWarehouse = wmsWarehouseService.getOne(new QueryWrapper<WmsWarehouse>().eq("warehouse_code", warehouseCode).eq("account_code", accountCode));
        if (wmsWarehouse != null) {
            return wmsWarehouse.getWarehouseName();
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getPurreturnDraft() {
        String redisKey = REDIS_KEY_START_TIME_PURRETURN_DRAFT; // Redis键
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_PURRETURN_DRAFT, redisKey, "采购退货草稿同步", dbName);

            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = jsonObject.getString("sign"); // 获取Sign作为accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    log.info("没有需要同步的采购退货草稿数据。");
                    continue;
                }

                try {
                    // 调用处理方法，传递 accountCode
                    processPurreturnDraftData(dataArray, accountCode);

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    // 同步出现异常，打印错误信息，不更新 Redis 中的时间
                    e.printStackTrace();
                    log.error("采购退货草稿数据同步出现异常：{}", e.getMessage());
                }
            } else {
                // 接口调用失败，错误信息
                log.error("采购退货草稿接口调用失败，错误信息：{}", jsonObject.getString("msg"));
                // 请求失败，不更新 Redis 中的时间
            }
        }
    }


    private void processPurreturnDraftData(JSONArray dataArray, String accountCode) {
        try {
            // 提取所有的采购退货编号（DocEntry）并转换为 List<String>
            List<String> serialNumberList = IntStream.range(0, dataArray.size()).mapToObj(dataArray::getJSONObject).map(item -> item.getString("DocEntry")).collect(Collectors.toList());

            // 批量查询已有的采购退货主表记录
            List<WmsPurreturnDraft> existingReturns = wmsPurReturnDraftService.list(new QueryWrapper<WmsPurreturnDraft>().in("serial_number", serialNumberList).eq("account_code", accountCode));

            // 将已有的记录放入 Map，使用 serialNumber + workNo 作为复合键，方便查找
            Map<String, WmsPurreturnDraft> existingReturnMap = existingReturns.stream().collect(Collectors.toMap(draft -> generateCompositeKey(draft.getSerialNumber(), draft.getWorkNo()), Function.identity(), (draft1, draft2) -> {
                log.warn("Duplicate key found for serialNumber: {}, workNo: {}. Keeping the first occurrence.", draft1.getSerialNumber(), draft1.getWorkNo());
                return draft1;
            }));

            // 遍历数据，处理每个采购退货
            IntStream.range(0, dataArray.size()).forEach(i -> {
                JSONObject item = dataArray.getJSONObject(i);

                // 处理采购退货主表数据
                WmsPurreturnDraft wmsPurReturnDraft = new WmsPurreturnDraft();
                wmsPurReturnDraft.setSerialNumber(item.getInteger("DocEntry")); // 单据标识
                wmsPurReturnDraft.setWorkNo(item.getString("DocNum")); // 单据号
                wmsPurReturnDraft.setPostDate(parseDateTime(item.getString("DocDate"))); // 过账日期
                wmsPurReturnDraft.setExpirationDate(parseDateTime(item.getString("DocDueDate"))); // 到期日期
                wmsPurReturnDraft.setSupplyCode(item.getString("CardCode")); // 供应商代码
                wmsPurReturnDraft.setSupplyName(item.getString("CardName")); // 供应商名称
                wmsPurReturnDraft.setFromErp(WmsConstant.FromErpEnum.YES.getValue());
                wmsPurReturnDraft.setErpSync(WmsConstant.ErpSyncEnum.INIT.getValue());
                wmsPurReturnDraft.setAccountCode(accountCode); // 账套代码
                wmsPurReturnDraft.setFormalDataFlag(WmsConstant.FormalDataFlagEnum.NO.getValue());

                // 生成复合键
                String compositeKey = generateCompositeKey(wmsPurReturnDraft.getSerialNumber(), wmsPurReturnDraft.getWorkNo());

                // 检查主表记录是否存在
                WmsPurreturnDraft existingReturn = existingReturnMap.get(compositeKey);

                // 初始化明细列表
                List<WmsPurreturnDraftDetail> returnDetailList = new ArrayList<>(); // 默认初始化为空列表

                // 处理采购退货明细数据
                Optional.ofNullable(item.getJSONArray("Lines")).ifPresent(detailArray -> IntStream.range(0, detailArray.size()).mapToObj(detailArray::getJSONObject).forEach(detailItem -> {
                    // 处理批次信息
                    JSONArray batchDetails = detailItem.getJSONArray("BatchDetails");
                    for (int j = 0; j < batchDetails.size(); j++) {
                        WmsPurreturnDraftDetail wmsPurReturnDetail = new WmsPurreturnDraftDetail();
                        JSONObject batchDetail = batchDetails.getJSONObject(j);
                        wmsPurReturnDetail.setSerialNumber(wmsPurReturnDraft.getSerialNumber()); // 单据标识
                        wmsPurReturnDetail.setWorkNo(wmsPurReturnDraft.getWorkNo()); // 单据号
                        wmsPurReturnDetail.setItemCode(detailItem.getString("ItemCode")); // 物料编号
                        wmsPurReturnDetail.setItemName(detailItem.getString("ItemName")); // 物料描述
                        wmsPurReturnDetail.setAllQty(detailItem.getDouble("Quantity")); // 退货数量
                        wmsPurReturnDetail.setBatchCode(batchDetail.getString("DistNumber")); // 批次号
                        wmsPurReturnDetail.setPlanQty(batchDetail.getDouble("Quantity")); // 批次数量
                        wmsPurReturnDetail.setItemSpec(detailItem.getString("U_Z98_GUIG")); // 规格
                        wmsPurReturnDetail.setItemUnit(detailItem.getString("InvntryUom")); // 库存计量单位
                        returnDetailList.add(wmsPurReturnDetail);
                    }
                }));

                // 如果主表已存在，则更新主表和子表
                if (existingReturn != null) {
                    if ("1".equals(existingReturn.getFormalDataFlag())) {
                        // formalDataFlag 为 "1"，不做任何操作
                        log.info("采购退货草稿已生成，formalDataFlag为1，不做处理。SerialNumber: {}, WorkNo: {}", existingReturn.getSerialNumber(), existingReturn.getWorkNo());
                    } else {
                        wmsPurReturnDraft.setId(existingReturn.getId());
                        // formalDataFlag 不为 "1"，更新该条记录
                        log.info("更新采购退货草稿记录。SerialNumber: {}, WorkNo: {}", wmsPurReturnDraft.getSerialNumber(), wmsPurReturnDraft.getWorkNo());
                        wmsPurReturnDraftService.updateMain(wmsPurReturnDraft, returnDetailList);
                    }
                } else {
                    // 插入主表和子表
                    log.info("插入新的采购退货草稿记录。SerialNumber: {}, WorkNo: {}", wmsPurReturnDraft.getSerialNumber(), wmsPurReturnDraft.getWorkNo());
                    wmsPurReturnDraftService.saveMain(wmsPurReturnDraft, returnDetailList);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理采购退货草稿数据时发生异常：{}", e.getMessage());
            throw new RuntimeException("处理采购退货草稿数据时发生异常", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getSalereturnDraft() {
        String redisKey = REDIS_KEY_START_TIME_SALERETURN_DRAFT; // Redis键，用于存储时间戳
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_SALERETURN_DRAFT, redisKey, "销售退货草稿同步", dbName);

            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = jsonObject.getString("sign"); // 获取Sign作为accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    log.info("没有需要同步的销售退货草稿数据。");
                    continue;
                }

                try {
                    // 调用处理方法，传递 accountCode
                    processSalereturnDraftData(dataArray, accountCode);

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    // 同步出现异常，打印错误信息，不更新 Redis 中的时间
                    e.printStackTrace();
                    log.error("销售退货草稿数据同步出现异常：{}", e.getMessage());
                }
            } else {
                // 接口调用失败，错误信息
                log.error("销售退货草稿接口调用失败，错误信息：{}", jsonObject.getString("msg"));
                // 请求失败，不更新 Redis 中的时间
            }
        }
    }

    private void processSalereturnDraftData(JSONArray dataArray, String accountCode) {
        try {
            // 提取所有的销售退货编号（DocEntry）并转换为 List<String>
            List<String> serialNumberList = IntStream.range(0, dataArray.size()).mapToObj(dataArray::getJSONObject).map(item -> item.getString("DocEntry")).collect(Collectors.toList());

            // 批量查询已有的销售退货主表记录
            List<WmsSalereturnDraft> existingReturns = wmsSaleReturnDraftService.list(new QueryWrapper<WmsSalereturnDraft>().in("serial_number", serialNumberList).eq("account_code", accountCode));

            // 将已有的记录放入 Map，使用 serialNumber + workNo 作为复合键，方便查找
            Map<String, WmsSalereturnDraft> existingReturnMap = existingReturns.stream().collect(Collectors.toMap(draft -> generateCompositeKey(draft.getSerialNumber(), draft.getWorkNo()), Function.identity(), (draft1, draft2) -> {
                log.warn("Duplicate key found for serialNumber: {}, workNo: {}. Keeping the first occurrence.", draft1.getSerialNumber(), draft1.getWorkNo());
                return draft1;
            }));

            // 遍历数据，处理每个销售退货
            IntStream.range(0, dataArray.size()).forEach(i -> {
                JSONObject item = dataArray.getJSONObject(i);

                // 处理销售退货主表数据
                WmsSalereturnDraft wmsSalereturnDraft = new WmsSalereturnDraft();
                wmsSalereturnDraft.setSerialNumber(item.getInteger("DocEntry")); // 单据标识
                wmsSalereturnDraft.setWorkNo(item.getString("DocNum")); // 单据号
                wmsSalereturnDraft.setCargoNo(item.getString("U_T016")); // 货号
                wmsSalereturnDraft.setPostDate(parseDateTime(item.getString("DocDate"))); // 过账日期
                wmsSalereturnDraft.setDeliveryDate(parseDateTime(item.getString("DocDueDate"))); // 交货日期
                wmsSalereturnDraft.setCustomerCode(item.getString("CardCode")); // 客户代码
                wmsSalereturnDraft.setCustomerName(item.getString("CardName")); // 客户名称
                wmsSalereturnDraft.setDeliveryType(item.getString("U_T040")); // 交货类型
                wmsSalereturnDraft.setRemark(item.getString("U_Z98_BAK1")); // 交货备注1
                wmsSalereturnDraft.setRemark2(item.getString("U_Z98_BAK2")); // 交货备注2
                wmsSalereturnDraft.setFromErp(WmsConstant.FromErpEnum.YES.getValue());
                wmsSalereturnDraft.setAccountCode(accountCode); // 账套代码
                wmsSalereturnDraft.setFormalDataFlag(WmsConstant.FormalDataFlagEnum.NO.getValue());

                // 生成复合键
                String compositeKey = generateCompositeKey(wmsSalereturnDraft.getSerialNumber(), wmsSalereturnDraft.getWorkNo());

                // 检查主表记录是否存在
                WmsSalereturnDraft existingReturn = existingReturnMap.get(compositeKey);

                // 初始化明细列表
                List<WmsSalereturnDraftDetail> returnDetailList = new ArrayList<>(); // 默认初始化为空列表

                // 处理销售退货明细数据
                Optional.ofNullable(item.getJSONArray("Lines")).ifPresent(detailArray -> IntStream.range(0, detailArray.size()).mapToObj(detailArray::getJSONObject).forEach(detailItem -> {
                    // 处理批次信息
                    JSONArray batchDetails = detailItem.getJSONArray("BatchDetails");
                    for (int j = 0; j < batchDetails.size(); j++) {
                        WmsSalereturnDraftDetail wmsSalereturnDetail = new WmsSalereturnDraftDetail();
                        JSONObject batchDetail = batchDetails.getJSONObject(j);
                        wmsSalereturnDetail.setSerialNumber(wmsSalereturnDraft.getSerialNumber()); // 单据标识
                        wmsSalereturnDetail.setWorkNo(wmsSalereturnDraft.getWorkNo()); // 单据号
                        wmsSalereturnDetail.setItemCode(detailItem.getString("ItemCode")); // 物料编号
                        wmsSalereturnDetail.setItemName(detailItem.getString("ItemName")); // 物料描述
                        wmsSalereturnDetail.setAllQty(detailItem.getDouble("Quantity")); // 交货数量
                        wmsSalereturnDetail.setBatchCode(batchDetail.getString("DistNumber")); // 批次号
                        wmsSalereturnDetail.setReceiveQty(batchDetail.getDouble("Quantity")); // 批次数量
                        wmsSalereturnDetail.setItemSpec(detailItem.getString("U_Z98_GUIG")); // 规格
                        wmsSalereturnDetail.setItemUnit(detailItem.getString("InvntryUom")); // 库存计量单位
                        wmsSalereturnDetail.setWarehouseCode(detailItem.getString("WhsCode")); // 仓库编号
                        wmsSalereturnDetail.setDeliveryTime(parseDateTime(detailItem.getString("U_L008")));
                        returnDetailList.add(wmsSalereturnDetail);
                    }
                }));

                // 如果主表已存在，则更新主表和子表
                if (existingReturn != null) {
                    if ("1".equals(existingReturn.getFormalDataFlag())) {
                        // formalDataFlag 为 "1"，不做任何操作
                        log.info("销售退货草稿已生成，formalDataFlag为1，不做处理。SerialNumber: {}, WorkNo: {}", existingReturn.getSerialNumber(), existingReturn.getWorkNo());
                    } else {
                        // formalDataFlag 不为 "1"，更新该条记录
                        wmsSalereturnDraft.setId(existingReturn.getId());
                        log.info("更新销售退货草稿记录。SerialNumber: {}, WorkNo: {}", wmsSalereturnDraft.getSerialNumber(), wmsSalereturnDraft.getWorkNo());
                        wmsSaleReturnDraftService.updateMain(wmsSalereturnDraft, returnDetailList);
                    }
                } else {
                    // 插入主表和子表
                    log.info("插入新的销售退货草稿记录。SerialNumber: {}, WorkNo: {}", wmsSalereturnDraft.getSerialNumber(), wmsSalereturnDraft.getWorkNo());
                    wmsSaleReturnDraftService.saveMain(wmsSalereturnDraft, returnDetailList);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理销售退货草稿数据时发生异常：{}", e.getMessage());
            throw new RuntimeException("处理销售退货草稿数据时发生异常", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getInspection() {
        String redisKey = REDIS_KEY_START_TIME_INSPECTION; // Redis键，用于存储时间戳
        for (String dbName : DB_NAMES) {
            JSONObject jsonObject = getDataFromApi(ENDPOINT_GET_INSPECTION, redisKey, "检验记录同步", dbName);
            if (jsonObject.getBoolean("result")) {
                JSONArray dataArray = jsonObject.getJSONArray("data");
                String accountCode = dbName; // 获取Sign作为accountCode

                if (dataArray == null || dataArray.isEmpty()) {
                    log.info("没有需要同步的检验记录数据。");
                    continue;
                }

                try {
                    // 调用处理方法，传递 accountCode
                    processInspectionData(dataArray, accountCode);

                    // 同步成功，更新 Redis 中的时间
                    String currentTime = LocalDate.now().atTime(0, 0, 0).format(DATE_TIME_FORMATTER);
                    stringRedisTemplate.opsForValue().set(redisKey + "_" + dbName, currentTime);

                } catch (Exception e) {
                    // 同步出现异常，打印错误信息，不更新 Redis 中的时间
                    e.printStackTrace();
                    log.error("检验记录数据同步出现异常：{}", e.getMessage());
                }
            } else {
                // 接口调用失败，错误信息
                log.error("检验记录接口调用失败，错误信息：{}", jsonObject.getString("msg"));
                // 请求失败，不更新 Redis 中的时间
            }
        }
             // 处理库存状态
         processInventoryStatus();
    }

    private void processInspectionData(JSONArray dataArray, String accountCode) {
        try {
            // 提取所有的检验单据编号（DocEntry）并转换为 List<String>
            List<String> docEntryList = IntStream.range(0, dataArray.size()).mapToObj(dataArray::getJSONObject).map(item -> item.getString("DocEntry")).collect(Collectors.toList());

            // 批量查询已有的检验记录
            List<WmsInspect> existingInspections = wmsInspectService.list(new QueryWrapper<WmsInspect>().in("serial_no", docEntryList).eq("account_code", accountCode));

            // 将已有的记录放入 Map，使用 serialNumber + workNo 作为复合键，方便查找
            Map<String, WmsInspect> existingInspectionMap = existingInspections.stream().collect(Collectors.toMap(inspect -> generateCompositeKey(inspect.getSerialNo(), inspect.getWorkNo()), Function.identity(), (inspect1, inspect2) -> {
                log.warn("发现重复键，serialNumber: {}, workNo: {}。保留第一个记录。", inspect1.getSerialNo(), inspect1.getWorkNo());
                return inspect1;
            }));

            // 遍历数据，处理每个检验记录
            IntStream.range(0, dataArray.size()).forEach(i -> {
                JSONObject item = dataArray.getJSONObject(i);

                // 处理检验主表数据
                WmsInspect wmsInspect = new WmsInspect();
                wmsInspect.setSerialNo(item.getInteger("DocEntry")); // 单据标识
                wmsInspect.setWorkNo(item.getString("DocNum")); // 单据号
                wmsInspect.setSupplyCode(item.getString("U_Z12_CDCD")); // 供应商代码
                wmsInspect.setSupplyName(item.getString("U_Z12_CDNM")); // 供应商名称
                wmsInspect.setInspectBy(item.getString("U_Z12_APUR")); // 检验员
                wmsInspect.setInspectDate(parseDateTime(item.getString("CreateDate"))); // 检验日期
                wmsInspect.setFromErp(WmsConstant.FromErpEnum.YES.getValue());
                wmsInspect.setErpSync(WmsConstant.ErpSyncEnum.INIT.getValue());
                wmsInspect.setAccountCode(accountCode); // 账套代码
                wmsInspect.setUpdateTime(new Date());

                // 生成复合键
                String compositeKey = generateCompositeKey(wmsInspect.getSerialNo(), wmsInspect.getWorkNo());

                // 检查主表记录是否存在
                WmsInspect existingInspection = existingInspectionMap.get(compositeKey);

                // 初始化明细列表
                List<WmsInspectdetail> inspectDetailList = new ArrayList<>(); // 默认初始化为空列表

                // 处理检验明细数据
                Optional.ofNullable(item.getJSONArray("Lines")).ifPresent(detailArray -> IntStream.range(0, detailArray.size()).mapToObj(detailArray::getJSONObject).forEach(detailItem -> {
                    // 处理批次信息
                    if (detailItem != null && !detailItem.isEmpty()) {
                        WmsInspectdetail wmsInspectDetail = new WmsInspectdetail();
                        wmsInspectDetail.setBillNo(wmsInspect.getWorkNo()); // 单据号
                        wmsInspectDetail.setPurchaseNo(detailItem.getString("U_Z12_PDOC")); // 采购订单
                        wmsInspectDetail.setItemCode(detailItem.getString("U_Z12_IITM")); // 物料编号
                        wmsInspectDetail.setItemName(detailItem.getString("U_Z12_INAM")); // 物料描述
                        wmsInspectDetail.setInspectMode(detailItem.getString("U_Z12_TYPE")); // 检验方式
                        wmsInspectDetail.setInspectQty(detailItem.getDouble("U_Z12_SQTY")); // 检验数量
                        wmsInspectDetail.setStandardQty(detailItem.getDouble("U_Z12_CQTY")); // 合格数量
                        wmsInspectDetail.setUnstandardQty(detailItem.getDouble("U_Z12_DQTY")); // 不合格数量
                        wmsInspectDetail.setInspectResult(detailItem.getString("U_Z12_QRES")); // 检验结果
                        wmsInspectDetail.setProcessResult(detailItem.getString("U_Z12_DRES")); // 处理结果
                        wmsInspectDetail.setItemSpec(detailItem.getString("U_Z98_GUIG")); // 规格
                        wmsInspectDetail.setItemUnit(detailItem.getString("InvntryUom")); // 库存计量单位

                        inspectDetailList.add(wmsInspectDetail);
                    }

                }));

                // 如果主表已存在，则更新主表和子表
                if (existingInspection != null) {
                    wmsInspect.setId(existingInspection.getId());
                    wmsInspectService.updateMain(wmsInspect, inspectDetailList);
                } else {
                    // 插入主表和子表
                    wmsInspectService.saveMain(wmsInspect, inspectDetailList);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理检验记录数据时发生异常：{}", e.getMessage());
            throw new RuntimeException("处理检验记录数据时发生异常", e);
        }
    }

    private void processInventoryStatus() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -3);
            Date threeDaysAgo = calendar.getTime();

            // 查询条件：创建时间在最近三天内
            QueryWrapper<WmsInspect> inspectWrapper = new QueryWrapper<>();
            inspectWrapper.ge("create_time", threeDaysAgo)
                    .or() // 添加 OR 条件
                    .ge("update_time", threeDaysAgo)
                    .orderByDesc("create_time");
            List<WmsInspect> recentInspections = wmsInspectService.list(inspectWrapper);

            if (recentInspections.isEmpty()) {
                log.info("没有找到对应的检验记录，跳过库存状态处理。");
                return;
            }
            // 获取所有的检验明细
            List<String> inspectIds = recentInspections.stream()
                    .map(WmsInspect::getId)
                    .collect(Collectors.toList());

            // 查询这些检验单的明细
            QueryWrapper<WmsInspectdetail> detailWrapper = new QueryWrapper<>();
            detailWrapper.in("bill_id", inspectIds);
            List<WmsInspectdetail> allRecentDetails = wmsInspectDetailService.list(detailWrapper);

            if (allRecentDetails.isEmpty()) {
                log.info("检验记录没有明细数据，跳过库存状态处理。");
                return;
            }

            // 创建一个Map记录每个采购订单+物料的处理情况，确保同一物料只同步一次
            Map<String, Boolean> syncedItemMap = new HashMap<>();

            // 处理每一个检验明细
            for (WmsInspectdetail detail : allRecentDetails) {
                // 获取检验单主表记录，用于获取accountCode
                WmsInspect wmsInspect = recentInspections.stream()
                        .filter(inspect -> inspect.getId().equals(detail.getBillId()))
                        .findFirst()
                        .orElse(null);

                if (wmsInspect == null) {
                    log.warn("未找到检验明细对应的主表记录，明细ID：{}", detail.getId());
                    continue;
                }

                String accountCode = wmsInspect.getAccountCode();

                // 获取采购订单号和物料代码
                String purchaseNo = detail.getPurchaseNo();
                String itemCode = detail.getItemCode();

                if (StringUtils.isBlank(purchaseNo) || StringUtils.isBlank(itemCode)) {
                    log.warn("检验明细数据不完整，采购订单号或物料代码为空，跳过处理。");
                    continue;
                }

                // 创建一个唯一键，用于检查是否已处理过该采购订单+物料组合
                String syncKey = purchaseNo + "_" + itemCode + "_" + accountCode;

                // 检查是否已经处理过该组合
                if (syncedItemMap.containsKey(syncKey)) {
                    log.info("采购订单 [{}] 物料 [{}] 已经在本次处理中同步过，跳过重复处理。", purchaseNo, itemCode);
                    continue;
                }

                // 根据工作号查询收货主单据
                QueryWrapper<WmsReceive> receiveWrapper = new QueryWrapper<>();
                receiveWrapper.eq("work_no", purchaseNo)
                        .eq("account_code", accountCode);
                List<WmsReceive> receiveList = wmsReceiveService.list(receiveWrapper);

                if (receiveList.isEmpty()) {
                    log.info("未找到采购订单号为 [{}] 的收货记录，跳过该明细的库存状态处理。", purchaseNo);
                    continue;
                }

                // 获取处理结果
                String processResult = detail.getProcessResult();

                // 对每一个找到的收货单进行处理
                for (WmsReceive receive : receiveList) {
                    // 查询收货明细
                    QueryWrapper<WmsReceivedetail> receiveDetailWrapper = new QueryWrapper<>();
                    receiveDetailWrapper.eq("bill_id", receive.getId())
                                        .eq("item_number", itemCode)
                                        .in("line_state", Arrays.asList(
                                                WmsConstant.ReceiveDetailLineStateEnum.INBOUND_COMPLETE.getValue(),
                                                WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_FAIL.getValue()
                                           ));

                    List<WmsReceivedetail> receiveDetails = wmsReceivedetailService.list(receiveDetailWrapper);

                    if (receiveDetails.isEmpty()) {
                        continue;
                    }

                    // 处理每个收货明细对应的库存
                    for (WmsReceivedetail receiveDetail : receiveDetails) {
                        // 查询对应的库存明细记录
                        QueryWrapper<WmsStockdetail> stockDetailWrapper = new QueryWrapper<>();
                        stockDetailWrapper.eq("bill_dtl_id", receiveDetail.getId())
                                .eq("item_code", itemCode);

                        List<WmsStockdetail> stockList = wmsStockdetailService.list(stockDetailWrapper);

                        if (stockList.isEmpty()) {
                            log.info("收货明细 [{}] 无对应物料 [{}] 的库存记录，跳过库存状态处理。",
                                    receiveDetail.getLineNo(), itemCode);
                            continue;
                        }

                        // 根据处理结果执行不同的库存操作
                        for (WmsStockdetail stock : stockList) {
                            if ("A".equals(processResult) || "C".equals(processResult)) {
                                // A或C - 取消禁用状态并同步给SAP
                                stock.setIsForbid("0");
                                stock.setUpdateTime(new Date());
                                wmsStockdetailService.updateById(stock);

                                // 检查该收货明细是否已经成功同步过（通过行状态判断）
                                if (WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(receiveDetail.getLineState())) {
                                    log.info("收货明细 [{}] 已经成功同步过，跳过重复同步。", receiveDetail.getId());
                                    continue;
                                }

                                // 同步给SAP，并记录同步状态到Map中
                                // 使用Redis检查是否已同步过，作为第二层防护
                                String syncCacheKey = "wms:sap:sync:inventory:" + receiveDetail.getId();
                                boolean alreadySynced = false;
                                try {
                                    Object cachedValue = redisUtil.get(syncCacheKey);
                                    if (cachedValue != null) {
                                        log.info("收货明细 [{}] 在本次同步过程中已经同步过，跳过重复同步。", receiveDetail.getId());
                                        alreadySynced = true;
                                        syncedItemMap.put(syncKey, true);
                                    }
                                } catch (Exception e) {
                                    log.warn("检查Redis缓存状态时发生异常，将继续执行同步: {}", e.getMessage());
                                }

                                boolean syncResult = false;
                                if (!alreadySynced) {
                                    // 设置Redis缓存，标记该收货明细正在同步
                                    try {
                                        redisUtil.set(syncCacheKey, "1", 30 * 6); // 设置3分钟过期时间，保证长时间同步任务也能覆盖
                                    } catch (Exception e) {
                                        log.warn("设置Redis缓存时发生异常: {}", e.getMessage());
                                    }

                                    syncResult = syncInventoryToSAP(receiveDetail);
                                    if (syncResult) {
                                        syncedItemMap.put(syncKey, true);
                                        log.info("处理结果为[{}]，物料[{}]的库存状态已更新为非禁用并成功同步到SAP。",
                                                processResult, itemCode);
                                    }else{
                                        syncedItemMap.put(syncKey, false);
                                        log.info("处理结果为[{}]，物料[{}]的库存状态同步到SAP失败。",
                                                processResult, itemCode);
                                    }
                                }
                            } else if ("B".equals(processResult)) {
                                // B - 删除对应的库存
                                wmsStockdetailService.removeById(stock.getId());
                                receiveDetail.setCheckTag(WmsConstant.CheckTagEnum.CHECK_NOT_PASS.getValue());
                                wmsReceivedetailService.updateById(receiveDetail);
                                log.info("处理结果为B，已删除物料[{}]的库存记录。", itemCode);
                            } else if ("D".equals(processResult)) {
                                // D - 不做处理
                                receiveDetail.setCheckTag(WmsConstant.CheckTagEnum.CHECK_COMPLETE.getValue());
                                wmsReceivedetailService.updateById(receiveDetail);
                                log.info("检验单 [{}] 明细 [{}] 处理结果为 D，不做处理。",
                                        wmsInspect.getWorkNo(), receiveDetail.getLineNo());
                            } else {
                                log.warn("未知的处理结果：{}，不对物料[{}]的库存做任何处理。", processResult, itemCode);
                            }
                        }
                    }
                }
            }

            // 清除本次同步过程中使用的Redis缓存
            try {
                // 获取所有带有指定前缀的键
                String keyPattern = "wms:sap:sync:inventory:*";
                // 获取所有匹配模式的键
                Set<String> keys = stringRedisTemplate.keys(keyPattern);
                if (keys != null && !keys.isEmpty()) {
                    // 批量删除所有匹配的键
                    stringRedisTemplate.delete(keys);
                    log.info("已清除本次库存同步过程中的Redis缓存，共{}个键", keys.size());
                } else {
                    log.info("未找到需要清除的Redis缓存键");
                }
            } catch (Exception e) {
                log.error("清除Redis缓存时发生异常：{}", e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("处理库存状态时发生异常：{}", e.getMessage(), e);
            throw new RuntimeException("处理库存状态时发生异常", e);
        }
    }

    /**
     * 同步库存到SAP
     * @param receiveDetail 收货明细
     * @return 同步是否成功
     */
    private boolean syncInventoryToSAP(WmsReceivedetail receiveDetail) {
        // 查询收货主表信息
        WmsReceive receive = wmsReceiveService.getById(receiveDetail.getBillId());
        if (receive == null) {
            log.warn("收货单 [{}] 不存在，跳过同步。", receiveDetail.getBillId());
            return false;
        }

        // 再次检查是否已经成功同步过（双重检查）
        if (WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(receiveDetail.getLineState())) {
            log.info("收货明细 [{}] 已经成功同步过，跳过重复同步。", receiveDetail.getId());
            return false;
        }

        // 构建请求数据
        JSONObject requestJson = new JSONObject();
        requestJson.put("DBName", receive.getAccountCode());        // 账套代码
        requestJson.put("CardCode", receive.getSupplierCode());     // 供应商代码
        requestJson.put("CardName", receive.getSupplierName());     // 供应商名称
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date postDate = receive.getPostDate() != null ? receive.getPostDate() : new Date();
        String docDate = dateFormat.format(postDate);     // 过账日期
        Date dueDate = receive.getDeliveryDate() != null ? receive.getDeliveryDate() : new Date();
        String docDueDate = dateFormat.format(dueDate);   // 到期日
        requestJson.put("DocDate", dateFormat.format(new Date()));
        requestJson.put("DocDueDate", docDueDate);

        // 使用固定格式构建来源单号，不添加随机数，保证同一单据生成的来源单号相同
        requestJson.put("U_LYDH", receive.getBillNo() + "_" + receiveDetail.getLineNo());

        JSONArray linesArray = new JSONArray();
        JSONObject lineJson = new JSONObject();
        lineJson.put("ItemCode", receiveDetail.getItemNumber());  // 物料代码
        lineJson.put("ItemName", receiveDetail.getItemName());    // 物料名称
        lineJson.put("Quantity", receiveDetail.getInboundQty());  // 数量

        try {
            lineJson.put("BaseEntry", receive.getSerialNumber()); // 基础单据编号
        } catch (NumberFormatException e) {
            log.error("无法解析的 SerialNumber：{}", receive.getSerialNumber());
            lineJson.put("BaseEntry", null);
        }

        lineJson.put("BaseLine", receiveDetail.getLineNo()); // 基础单据行号

        // 构建批次信息
        JSONArray obtnsArray = new JSONArray();
        JSONObject obtnJson = new JSONObject();
        obtnJson.put("DistNumber", receiveDetail.getBatchCode()); // 批次号
        obtnJson.put("Quantity", receiveDetail.getInboundQty());  // 数量
        SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
        String mnfDate = dateFormat1.format(receiveDetail.getProductDate() == null ? new Date() : receiveDetail.getProductDate()); // 生产日期
        obtnJson.put("mnfdate", mnfDate);
        String expDate = dateFormat1.format(receiveDetail.getExpirationDate() == null ? new Date() : receiveDetail.getExpirationDate()); // 到期日期
        obtnJson.put("ExpDate", expDate);
        obtnsArray.add(obtnJson);
        lineJson.put("OBTNs", obtnsArray);

        linesArray.add(lineJson);
        requestJson.put("Lines", linesArray);

        // 发送请求
        String url = mainBaseUrl + ENDPOINT_GET_PURCHASE_RECEIVE;
        JSONObject jsonObject = executeRequest(url, requestJson, "采购入库单据标识：" + receive.getSerialNumber() + "同步给SAP");
//        JSONObject jsonObject = SapApiUtil.mockRequest(requestJson, "采购入库单据标识：" + receive.getSerialNumber() + "同步给SAP", baseCommonService, null);

        // 判断同步结果
        if (jsonObject.getBoolean("result")) {
            // 同步成功 -> 更新明细行状态为已同步
            receiveDetail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue());
            receiveDetail.setCheckTag(receiveDetail.getCheckTag().equals(WmsConstant.CheckTagEnum.CHECKING.getValue()) ? WmsConstant.CheckTagEnum.CHECK_COMPLETE.getValue() : receiveDetail.getCheckTag());
            receiveDetail.setUpdateTime(new Date());
            wmsReceivedetailService.updateById(receiveDetail);

            log.info("收货单 [{}] 物料 [{}] 同步成功。", receive.getBillNo(), receiveDetail.getItemNumber());
            return true;
        } else {
            // 同步失败 -> 更新主表 erp_sync 状态为同步失败
            String errMsg = jsonObject.getString("msg");
            log.error("收货单 [{}] 物料 [{}] 同步失败，错误信息：{}",
                    receive.getBillNo(), receiveDetail.getItemNumber(), errMsg);
            if(receiveDetail.getLineState().equals(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue())){
                return true;
            }
            receiveDetail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_FAIL.getValue());
            receiveDetail.setUpdateTime(new Date());
            wmsReceivedetailService.updateById(receiveDetail);
            return false;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncReceive() {
        try {
            // 1. 从子表 WmsReceivedetail 查询出 LineState = INBOUND_COMPLETE 的明细
            QueryWrapper<WmsReceivedetail> detailWrapper = new QueryWrapper<>();
            //只查询最近一个月的数据
            detailWrapper.ge("create_time", DateUtils.addMonths(new Date(), -1));
           //查询入库完成或者入库同步失败的数据
            detailWrapper.in("line_state", Arrays.asList(
                WmsConstant.ReceiveDetailLineStateEnum.INBOUND_COMPLETE.getValue(),
                WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_FAIL.getValue()
            ));
            List<WmsReceivedetail> allCompletedDetails = wmsReceivedetailService.list(detailWrapper);
            if (allCompletedDetails.isEmpty()) {
                log.info("没有满足行状态为入库完成且免检的收货明细数据。");
                return;
            }

            // 优化：先收集所有明细的 billId，批量查询主表数据
            Set<String> allBillIds = allCompletedDetails.stream()
                    .map(WmsReceivedetail::getBillId)
                    .collect(Collectors.toSet());

            // 批量查询主表数据
            Map<String, WmsReceive> allReceiveMap = wmsReceiveService.listByIds(allBillIds).stream()
                    .collect(Collectors.toMap(WmsReceive::getId, receive -> receive));

            // 筛选免检的物料对应的收货记录
            List<WmsReceivedetail> detailList = allCompletedDetails.stream()
                    .filter(detail -> {
                        // 从预先查询的主表Map中获取账套代码
                        WmsReceive receive = allReceiveMap.get(detail.getBillId());
                        if (receive == null) {
                            return false;
                        }

                        String itemCode = detail.getItemNumber();      // 物料代码
                        String accountCode = receive.getAccountCode(); // 账套代码

                        // 调用isExemptInspectionItem判断是否是免检物料
                        return wmsSpecMatchItemService.isExemptInspectionItem(itemCode, accountCode);
                    })
                    .collect(Collectors.toList());

            if (detailList.isEmpty()) {
                log.info("没有找到行状态为入库完成且物料为免检的收货明细数据。");
                return;
            }
            // 2. 将这些明细根据相同的 billId 分组
            //    billId 对应 WmsReceive 的主键 id
            Map<String, List<WmsReceivedetail>> detailGroupMap = detailList.stream().collect(Collectors.groupingBy(WmsReceivedetail::getBillId));

            // 3. 收集所有 billId，再根据原有条件从主表查询
            Set<String> billIdSet = detailGroupMap.keySet();
            if (billIdSet.isEmpty()) {
                log.info("没有可分组的 billId。");
                return;
            }

            // 构造主表查询条件
            QueryWrapper<WmsReceive> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", billIdSet);
            queryWrapper.eq("bill_type", "CGRK"); // 采购入库
            queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());

            List<WmsReceive> receiveList = wmsReceiveService.list(queryWrapper);
            if (receiveList.isEmpty()) {
                log.info("没有符合条件的收货单主表数据。");
                return;
            }

            // 将主表数据放入映射，方便根据 billId 获取对应的主表记录
            Map<String, WmsReceive> receiveMap = receiveList.stream().collect(Collectors.toMap(WmsReceive::getId, Function.identity()));

            // 在处理完成后清除缓存，避免长时间占用内存
            wmsSpecMatchItemService.clearExemptInspectionCache();

            // 4. 遍历分组明细，根据对应的主表信息做同步处理
            for (Map.Entry<String, List<WmsReceivedetail>> entry : detailGroupMap.entrySet()) {
                String billId = entry.getKey();
                List<WmsReceivedetail> wmsReceivedetails = entry.getValue();

                // 根据 billId 找到对应的主表
                WmsReceive receive = receiveMap.get(billId);
                if (receive == null) {
                    // 如果主表为空，说明不符合条件或已被过滤，跳过
                    continue;
                }

                if (wmsReceivedetails == null || wmsReceivedetails.isEmpty()) {
                    log.warn("收货单 [{}] 无明细数据，跳过同步。", receive.getBillNo());
                    continue;
                }

                // 检查是否已经有同步中的状态，避免重复同步
                boolean hasSyncingDetails = wmsReceivedetails.stream()
                        .anyMatch(detail -> WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNCING.getValue().equals(detail.getLineState()));
                if (hasSyncingDetails) {
                    log.warn("收货单 [{}] 有明细正在同步中，跳过本次同步。", receive.getBillNo());
                    continue;
                }


                // 先将所有明细状态更新为"同步中"，防止并发同步
                for (WmsReceivedetail detail : wmsReceivedetails) {
                    if (WmsConstant.ReceiveDetailLineStateEnum.INBOUND_COMPLETE.getValue().equals(detail.getLineState())) {
                        detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNCING.getValue());
                        detail.setUpdateTime(new Date());
                    }
                }
                wmsReceivedetailService.updateBatchById(wmsReceivedetails);
                    // 组装请求数据
                    JSONObject requestJson = new JSONObject();
                    requestJson.put("DBName", receive.getAccountCode());        // 账套代码
                    requestJson.put("CardCode", receive.getSupplierCode());     // 供应商代码
                    requestJson.put("CardName", receive.getSupplierName());     // 供应商名称

                    // 格式化日期为字符串
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date postDate = receive.getPostDate() != null ? receive.getPostDate() : new Date();
                    String docDate = dateFormat.format(postDate);     // 过账日期
                    Date dueDate = receive.getDeliveryDate() != null ? receive.getDeliveryDate() : new Date();
                    String docDueDate = dateFormat.format(dueDate);   // 到期日
                    requestJson.put("DocDate", dateFormat.format(new Date()));
                    requestJson.put("DocDueDate", docDueDate);

                    // 使用更可靠的唯一标识方式，避免随机数可能导致的重复
                    String uniqueId = receive.getBillNo() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                    requestJson.put("U_LYDH", uniqueId);

                    // 构建 Lines 数组（可能会有多行）
                    Map<String, JSONObject> linesMap = new HashMap<>();
                    SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");

                    for (WmsReceivedetail detail : wmsReceivedetails) {
                        // 如果行状态已同步，则跳过
                        if (WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(detail.getLineState())) {
                            continue;
                        }

                        // 按物料、基础单据编号及行号等信息分组，避免在 SAP 中出现多行合并
                        String key = detail.getItemNumber() + "_" + detail.getItemName() + "_" + receive.getSerialNumber() + "_" + detail.getLineNo();

                        JSONObject lineJson = linesMap.get(key);
                        if (lineJson == null) {
                            // 不存在则新建
                            lineJson = new JSONObject();
                            lineJson.put("ItemCode", detail.getItemNumber());  // 物料代码
                            lineJson.put("ItemName", detail.getItemName());    // 物料名称
                            lineJson.put("Quantity", detail.getInboundQty());  // 数量

                            try {
                                lineJson.put("BaseEntry", receive.getSerialNumber()); // 基础单据编号
                            } catch (NumberFormatException e) {
                                log.error("无法解析的 SerialNumber：{}", receive.getSerialNumber());
                                lineJson.put("BaseEntry", null);
                            }

                            lineJson.put("BaseLine", detail.getLineNo()); // 基础单据行号

                            // 初始化批次列表
                            JSONArray obtnsArray = new JSONArray();
                            lineJson.put("OBTNs", obtnsArray);

                            linesMap.put(key, lineJson);
                        } else {
                            // 如果已存在则累加数量
                            double currentQuantity = lineJson.getDoubleValue("Quantity");
                            lineJson.put("Quantity", currentQuantity + detail.getInboundQty());
                        }

                        // 构建批次信息
                        JSONArray obtnsArray = lineJson.getJSONArray("OBTNs");
                        JSONObject obtnJson = new JSONObject();
                        obtnJson.put("DistNumber", detail.getBatchCode()); // 批次号
                        obtnJson.put("Quantity", detail.getInboundQty());  // 数量
                        String mnfDate = dateFormat1.format(detail.getProductDate() == null ? new Date() : detail.getProductDate()); // 生产日期
                        obtnJson.put("mnfdate", mnfDate);
                        String expDate = dateFormat1.format(detail.getExpirationDate() == null ? new Date() : detail.getExpirationDate()); // 到期日期
                        obtnJson.put("ExpDate", expDate);
                        obtnsArray.add(obtnJson);
                    }

                    // 若没有可同步的行，直接跳过
                    if (linesMap.isEmpty()) {
                        log.warn("收货单 [{}] 明细均已同步或数量为 0，跳过。", receive.getBillNo());
                        continue;
                    }

                    // 组装到 Lines
                    JSONArray linesArray = new JSONArray();
                    linesArray.addAll(linesMap.values());
                    requestJson.put("Lines", linesArray);

                    log.info("准备同步收货单 [{}] 到SAP，请求数据: {}", receive.getBillNo(), requestJson.toJSONString());

                    // 发送请求
                    String url = mainBaseUrl + ENDPOINT_GET_PURCHASE_RECEIVE;
                    JSONObject jsonObject = executeRequest(url, requestJson, "采购入库单据标识：" + receive.getSerialNumber() + "同步给SAP");

                    // 判断同步结果
                    if (jsonObject.getBoolean("result")) {
                        // 同步成功 -> 更新明细行状态为已同步
                        wmsReceivedetails.forEach(detail -> {
                            if (!WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(detail.getLineState())) {
                                detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue());
                                detail.setUpdateTime(new Date());
                            }
                        });
                        wmsReceivedetailService.updateBatchById(wmsReceivedetails);
                        log.info("收货单 [{}] 同步成功，SAP返回: {}", receive.getBillNo(), jsonObject.toJSONString());
                    } else {
                        // 同步失败 -> 更新明细行状态为同步失败
                        wmsReceivedetails.forEach(detail -> {
                            if (!WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_FAIL.getValue().equals(detail.getLineState())) {
                                detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_FAIL.getValue());
                                detail.setUpdateTime(new Date());
                            }
                        });
                        wmsReceivedetailService.updateBatchById(wmsReceivedetails);
                        log.error("收货单 [{}] 同步失败，SAP返回: {}", receive.getBillNo(), jsonObject.toJSONString());
                    }
                }
        } catch (Exception e) {
            log.error("同步收货单到SAP过程中发生异常", e);
            throw e;
        }
    }


    @Override
    public void syncPurreturn() {
        // 1. 构建查询条件：只查询已完成但同步状态≠成功，且来自ERP的单据
        QueryWrapper<WmsPurchaseReturn> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
        queryWrapper.eq("bill_status", WmsConstant.PurchaseReturnBillStatusEnum.COMPLETE.getValue());
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());

        // 2. 查询采购退货单主表
        List<WmsPurchaseReturn> purchaseReturnList = wmsPurchaseReturnService.list(queryWrapper);
        if (purchaseReturnList == null || purchaseReturnList.isEmpty()) {
            log.info("没有需要同步的采购退货单数据。");
            return;
        }

        // 3. 遍历每条采购退货单，分别发送同步请求
        for (WmsPurchaseReturn purchaseReturn : purchaseReturnList) {
            try {
                // 在同步SAP之前，尝试更新对应的收货明细退货标记
                updateReceiveDetailReturnTag(purchaseReturn);

                // 3.1 组装请求体
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", purchaseReturn.getAccountCode());
                requestJson.put("DocEntry", purchaseReturn.getSerialNumber()); // 来源单据标识(采购退货草稿标识)

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = (purchaseReturn.getPostDate() != null ? purchaseReturn.getPostDate() : new Date());
                String docDate = dateFormat.format(postDate); // 过账日期

                Date dueDate = (purchaseReturn.getExpirationDate() != null ? purchaseReturn.getExpirationDate() : new Date());
                String docDueDate = dateFormat.format(dueDate); // 到期日

                requestJson.put("DocDate", docDate);
                requestJson.put("DocDueDate", docDueDate);
                requestJson.put("U_LYDH", purchaseReturn.getBillNo()); // WMS/MES来源单号

                // 拼接URL
                String url = mainBaseUrl + ENDPOINT_GET_PURCHASE_RETURN;

                // 3.2 发送请求到 SAP
                JSONObject jsonObject = executeRequest(url, requestJson, "采购退货订单号：" + purchaseReturn.getWorkNo() + " 同步给SAP");

                // 3.3 根据结果更新采购退货单状态
                if (jsonObject.getBoolean("result")) {
                    // 同步成功
                    purchaseReturn.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                    purchaseReturn.setUpdateTime(new Date());
                    wmsPurchaseReturnService.updateById(purchaseReturn);

                    log.info("采购退货单 [{}] 同步成功。", purchaseReturn.getBillNo());
                } else {
                    // 同步失败：记录错误信息，并更新状态为 FAIL
                    String errorMsg = jsonObject.getString("msg");
                    log.error("采购退货单 [{}] 同步失败，错误信息：{}", purchaseReturn.getBillNo(), errorMsg);

                    purchaseReturn.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                    purchaseReturn.setUpdateTime(new Date());
                    wmsPurchaseReturnService.updateById(purchaseReturn);
                }
            } catch (Exception e) {
                // 3.4 出现系统异常/运行时异常，也只影响本条记录
                log.error("采购退货单 [{}] 同步过程中出现异常：{}", purchaseReturn.getBillNo(), e.getMessage(), e);

                // 标记为同步失败
                purchaseReturn.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                purchaseReturn.setUpdateTime(new Date());
                wmsPurchaseReturnService.updateById(purchaseReturn);
            }
        }
    }

    /**
     * 更新收货明细的退货标记
     * 根据采购退货的采购订单号和账套信息查询收货记录，并根据退货明细中的品号和批号更新对应收货明细的退货标记
     *
     * @param purchaseReturn 采购退货单
     */
    private void updateReceiveDetailReturnTag(WmsPurchaseReturn purchaseReturn) {
        try {
            // 获取采购退货的采购订单号和账套信息
            String purchaseOrderNo = purchaseReturn.getWorkNo(); // 采购订单号
            String accountCode = purchaseReturn.getAccountCode(); // 账套信息

            if (StringUtils.isBlank(purchaseOrderNo) || StringUtils.isBlank(accountCode)) {
                log.warn("采购退货单 [{}] 缺少采购订单号或账套信息，无法更新收货明细退货标记", purchaseReturn.getBillNo());
                return;
            }

            // 1. 查询对应的采购收货单（bill_type为CGRK）
            QueryWrapper<WmsReceive> receiveQueryWrapper = new QueryWrapper<>();
            receiveQueryWrapper.eq("bill_type", "CGRK");
            receiveQueryWrapper.eq("work_no", purchaseOrderNo);
            receiveQueryWrapper.eq("account_code", accountCode);
            List<WmsReceive> receiveList = wmsReceiveService.list(receiveQueryWrapper);

            if (receiveList == null || receiveList.isEmpty()) {
                log.warn("未找到与采购退货单 [{}] 对应的收货单，采购订单号: {}, 账套: {}",
                        purchaseReturn.getBillNo(), purchaseOrderNo, accountCode);
                return;
            }

            // 2. 获取采购退货单明细
            QueryWrapper<WmsPurchaseReturnDetail> detailQueryWrapper = new QueryWrapper<>();
            detailQueryWrapper.eq("bill_id", purchaseReturn.getId());
            List<WmsPurchaseReturnDetail> returnDetailList = wmsPurchaseReturnDetailService.list(detailQueryWrapper);

            if (returnDetailList == null || returnDetailList.isEmpty()) {
                log.warn("采购退货单 [{}] 没有明细数据，无法更新收货明细退货标记", purchaseReturn.getBillNo());
                return;
            }

            // 3. 遍历所有退货明细，更新对应的收货明细退货标记
            for (WmsPurchaseReturnDetail returnDetail : returnDetailList) {
                String itemCode = returnDetail.getItemCode(); // 品号
                String batchCode = returnDetail.getBatchCode(); // 批号

                if (StringUtils.isBlank(itemCode)) {
                    continue; // 跳过没有品号的明细
                }

                // 遍历所有相关收货单
                for (WmsReceive receive : receiveList) {
                    // 查询对应的收货明细
                    QueryWrapper<WmsReceivedetail> receiveDetailWrapper = new QueryWrapper<>();
                    receiveDetailWrapper.eq("bill_id", receive.getId());
                    receiveDetailWrapper.eq("item_number", itemCode);

                    // 如果批号不为空，加上批号条件
                    if (StringUtils.isNotBlank(batchCode)) {
                        receiveDetailWrapper.eq("batch_code", batchCode);
                    }

                    // 查询符合条件的收货明细
                    List<WmsReceivedetail> receiveDetailList = wmsReceivedetailService.list(receiveDetailWrapper);

                    if (receiveDetailList != null && !receiveDetailList.isEmpty()) {
                        // 更新收货明细的退货标记
                        for (WmsReceivedetail receiveDetail : receiveDetailList) {
                            receiveDetail.setReturnTag("1"); // 设置退货标记为1
                            receiveDetail.setUpdateTime(new Date());
                            receiveDetail.setUpdateBy(purchaseReturn.getUpdateBy());
                            wmsReceivedetailService.updateById(receiveDetail);

                            log.info("成功更新收货明细退货标记，收货单: {}, 品号: {}, 批号: {}",
                                    receive.getBillNo(), itemCode, batchCode);
                        }
                    } else {
                        log.warn("未找到与退货明细对应的收货明细，收货单: {}, 品号: {}, 批号: {}",
                                receive.getBillNo(), itemCode, batchCode);
                    }
                }
            }

            log.info("采购退货单 [{}] 的收货明细退货标记更新操作已完成", purchaseReturn.getBillNo());
        } catch (Exception e) {
            // 记录错误但不影响主流程
            log.error("更新采购退货单 [{}] 对应的收货明细退货标记时发生异常: {}",
                    purchaseReturn.getBillNo(), e.getMessage(), e);
        }
    }


    @Override
    public void syncSales() {
        // 1. 构建查询条件：只查询"已发货完成、ERP 未同步成功、来自ERP且单据类型为 XSCK"的记录
        QueryWrapper<WmsSend> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
        queryWrapper.eq("bill_status", WmsConstant.SendBillStatusEnum.SEND_COMPLETE.getValue());
        queryWrapper.eq("bill_type", "XSCK");
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());

        // 2. 查询出库管理信息
        List<WmsSend> sendList = wmsSendService.list(queryWrapper);
        if (sendList == null || sendList.isEmpty()) {
            log.info("没有需要同步的出库管理数据。");
            return;
        }

        // 3. 遍历每条记录，各自发送同步请求
        for (WmsSend send : sendList) {
            try {
                // 3.1 组装请求数据
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", send.getAccountCode()); // 账套代码
                requestJson.put("DocEntry", send.getSerialNumber()); // 来源单据标识(销售交货草稿标识)

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = (send.getPostDate() != null ? send.getPostDate() : new Date());
                String docDate = dateFormat.format(postDate); // 过账日期

                Date dueDate = (send.getDeliveryDate() != null ? send.getDeliveryDate() : new Date());
                String docDueDate = dateFormat.format(dueDate); // 到期日

                requestJson.put("DocDate", docDate);
                requestJson.put("DocDueDate", docDueDate);
                requestJson.put("U_LYDH", send.getBillNo()); // WMS/MES来源单号

                // 3.2 发送请求到 SAP
                String url = mainBaseUrl + ENDPOINT_GET_PURCHASE_DELIVERY;
                JSONObject jsonObject = executeRequest(url, requestJson, "销售出库单据标识：" + send.getSerialNumber() + " 同步给SAP");

                // 3.3 处理响应结果
                if (jsonObject.getBoolean("result")) {
                    // 同步成功
                    send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                    send.setUpdateTime(new Date());
                    wmsSendService.updateById(send);

                    log.info("出库单 [{}] 同步成功。", send.getBillNo());
                } else {
                    // 同步失败
                    String errorMsg = jsonObject.getString("msg");
                    log.error("销售订单 [{}] 同步失败，错误信息：{}", send.getBillNo(), errorMsg);

                    send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                    send.setUpdateTime(new Date());
                    wmsSendService.updateById(send);
                }

            } catch (Exception e) {
                // 3.4 捕获当前记录的异常，记录日志并标记为失败，继续处理下一条
                log.error("销售订单 [{}] 同步时出现异常：{}", send.getBillNo(), e.getMessage(), e);

                send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                send.setUpdateTime(new Date());
                wmsSendService.updateById(send);

                // 注意这里不抛出异常，以免中断整个循环
            }
        }
    }


    @Override
    public void syncSalereturn() {
        // 1. 构建查询条件：仅查询需同步的销售退货入库单 (bill_type = XSTHRK)
        QueryWrapper<WmsReceive> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
        queryWrapper.eq("bill_status", WmsConstant.ReceiveBillStatusEnum.RECEIVE_COMPLETE.getValue());
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        queryWrapper.eq("bill_type", "XSTHRK");

        // 2. 查询符合条件的记录
        List<WmsReceive> list = wmsReceiveService.list(queryWrapper);
        if (list == null || list.isEmpty()) {
            log.info("没有需要同步的销售退货入库数据（WmsReceive，bill_type=XSTHRK）。");
            return;
        }

        // 3. 遍历，每条记录各自发起同步
        for (WmsReceive wmsReceive : list) {
            try {
                // 3.1 组装请求数据
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", wmsReceive.getAccountCode());     // 账套代码
                requestJson.put("DocEntry", wmsReceive.getSerialNumber());  // 来源单据标识

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = (wmsReceive.getPostDate() != null ? wmsReceive.getPostDate() : new Date());
                String docDate = dateFormat.format(postDate);

                Date dueDate = (wmsReceive.getDeliveryDate() != null ? wmsReceive.getDeliveryDate() : new Date());
                String docDueDate = dateFormat.format(dueDate);

                requestJson.put("DocDate", docDate);
                requestJson.put("DocDueDate", docDueDate);
                requestJson.put("U_LYDH", wmsReceive.getBillNo());          // WMS/MES来源单号

                // 3.2 调用外部接口
                String url = mainBaseUrl + ENDPOINT_GET_SALES_RETURN;
                JSONObject jsonObject = executeRequest(url, requestJson, "销售退货工单编号：" + wmsReceive.getWorkNo() + " 同步给SAP");

                // 3.3 处理响应结果
                if (jsonObject.getBoolean("result")) {
                    // 同步成功 => 更新当前记录
                    wmsReceive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                    wmsReceive.setUpdateTime(new Date());
                    wmsReceiveService.updateById(wmsReceive);

                    log.info("销售退货入库单 [{}] 同步成功。", wmsReceive.getBillNo());
                } else {
                    // 同步失败 => 记录错误并置为FAIL
                    String msg = jsonObject.getString("msg");
                    log.error("销售退货入库单 [{}] 同步失败，错误信息：{}", wmsReceive.getBillNo(), msg);

                    wmsReceive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                    wmsReceive.setUpdateTime(new Date());
                    wmsReceiveService.updateById(wmsReceive);
                }

            } catch (Exception e) {
                // 3.4 捕获当前记录的异常 => 不再向外抛，让循环继续下去
                log.error("销售退货入库单 [{}] 同步时出现异常：{}", wmsReceive.getBillNo(), e.getMessage(), e);

                // 将此单据同步状态置为FAIL，方便后续追踪
                wmsReceive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                wmsReceive.setUpdateTime(new Date());
                wmsReceiveService.updateById(wmsReceive);
            }
        }
    }


    @Override
    public void syncProduceReceive() {
        // 1. 查询需要同步的主单据
        QueryWrapper<WmsReceive> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("bill_type", "SCRK", "SCZC");
        queryWrapper.eq("bill_status", WmsConstant.ReceiveBillStatusEnum.RECEIVE_COMPLETE.getValue());
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());

        List<WmsReceive> receiveList = wmsReceiveService.list(queryWrapper);
        if (receiveList == null || receiveList.isEmpty()) {
            log.info("syncProduceReceiveBatch => 无需同步的生产入库单数据。");
            return;
        }

        // 2. 逐条处理
        for (WmsReceive receive : receiveList) {
            try {
                // 调用"单条同步"逻辑
                syncProduceReceiveSingle(receive.getId());
            } catch (Exception e) {
                // 此处捕获异常，保证不会影响下一个单据
                log.error("单据 [{}] 同步失败，原因: {}", receive.getBillNo(), e.getMessage(), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncProduceReceiveSingle(String receiveId) {
        // 1. 获取主单据和明细
        WmsReceive receive = wmsReceiveService.getById(receiveId);
        if (receive == null) {
            log.warn("syncProduceReceiveSingle => 未找到主单据ID={}", receiveId);
            return;
        }

        List<WmsReceivedetail> details = wmsReceivedetailService.selectByMainId(receiveId);
        if (details == null || details.isEmpty()) {
            log.warn("生产入库单 [{}] 无明细数据，跳过同步。", receive.getBillNo());
            return;
        }

        // 2. 判断明细是否全部已同步
        boolean allLinesSynced = details.stream().allMatch(d -> WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(d.getLineState()));
        if (allLinesSynced) {
            // 如果主单据还不是成功，顺便更新一下
            if (!WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue().equals(receive.getErpSync())) {
                receive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                receive.setUpdateTime(new Date());
                wmsReceiveService.updateById(receive);
                log.info("生产入库单 [{}] 所有明细已是成功状态，主单据更新为 SYNC_SUCCESS。", receive.getBillNo());
            }
            return;
        }

        // 3. 组装 SAP 请求
        JSONObject requestJson = new JSONObject();
        requestJson.put("DBName", receive.getAccountCode());

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date postDate = (receive.getPostDate() != null ? receive.getPostDate() : new Date());
        String docDate = dateFormat.format(postDate);
        requestJson.put("DocDate", docDate);

        requestJson.put("U_LYDH", receive.getBillNo());

        JSONArray linesArray = new JSONArray();
        JSONObject lineJson = new JSONObject();

        double totalQuantity = 0.0;
        JSONArray obtnsArray = new JSONArray();
        SimpleDateFormat batchDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (WmsReceivedetail detail : details) {
            if (WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(detail.getLineState())) {
                // 已同步的明细直接跳过
                continue;
            }
            totalQuantity += detail.getInboundQty();

            JSONObject obtnJson = new JSONObject();
            obtnJson.put("DistNumber", detail.getBatchCode());
            obtnJson.put("Quantity", detail.getInboundQty());

            if (detail.getProductDate() != null) {
                obtnJson.put("mnfdate", batchDateFormat.format(detail.getProductDate()));
            }
            obtnsArray.add(obtnJson);
        }

        if (totalQuantity == 0.0) {
            // 全部明细都跳过 => 已经是同步成功状态
            log.warn("生产入库单 [{}] 全部明细已同步，无需再次同步。", receive.getBillNo());

            // 若主单据依旧不是成功，也更新一下
            if (!WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue().equals(receive.getErpSync())) {
                receive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                receive.setUpdateTime(new Date());
                wmsReceiveService.updateById(receive);
                log.info("生产入库单 [{}] 主单据更新为 SYNC_SUCCESS。", receive.getBillNo());
            }
            return;
        }

        // 构建行对象
        lineJson.put("Quantity", totalQuantity);
        try {
            lineJson.put("BaseEntry", Integer.parseInt(receive.getWorkNo()));
        } catch (NumberFormatException e) {
            log.error("无法解析 WorkNo [{}] 为整数。", receive.getWorkNo(), e);
            lineJson.put("BaseEntry", null);
        }
        lineJson.put("OBTNs", obtnsArray);

        linesArray.add(lineJson);
        requestJson.put("Lines", linesArray);

        // 4. 调用SAP接口
        String url = mainBaseUrl + ENDPOINT_GET_PRODUCTION_RECEIVE;
        JSONObject sapResult;
        try {
            sapResult = executeRequest(url, requestJson, "生产入库工单编号：" + receive.getWorkNo() + " 同步到SAP");
        } catch (Exception e) {
            // 调用接口出现异常 => 回滚当前单据
            log.error("调用SAP接口异常，单据 [{}] 同步中断。", receive.getBillNo(), e);
            throw new RuntimeException("调用SAP接口异常", e);
        }

        // 5. 根据SAP返回结果更新
        if (sapResult.getBoolean("result")) {
            // 同步成功 => 更新主表 & 明细状态
            receive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
            receive.setUpdateTime(new Date());
            wmsReceiveService.updateById(receive);

            for (WmsReceivedetail detail : details) {
                if (!WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(detail.getLineState())) {
                    detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue());
                    detail.setUpdateTime(new Date());
                }
            }
            wmsReceivedetailService.updateBatchById(details);

            log.info("生产入库单 [{}] 同步SAP成功，主单据 + 明细均更新为成功。", receive.getBillNo());
        } else {
            // 同步失败 => 抛异常回滚当前单据
            String msg = sapResult.getString("msg");
            log.error("生产入库单 [{}] 同步失败，SAP错误信息: {}", receive.getBillNo(), msg);

            // 标记主单据为失败
            receive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
            receive.setUpdateTime(new Date());
            wmsReceiveService.updateById(receive);
        }
    }

    private void deductInventory(WmsStockDump dump) {
        // 1. 获取源单行ID，并查询对应的出库明细
        String srcLineId = dump.getSrcLineId();
        if (srcLineId == null) {
            throw new RuntimeException("源单行ID为空，无法扣减库存");
        }

        WmsSenddetail sendDetail = wmsSendDetailService.getById(srcLineId);
        if (sendDetail == null) {
            throw new RuntimeException("未找到对应的出库明细，无法扣减库存");
        }

        double remainingQty = sendDetail.getPlanQty(); // 使用出库明细的 planQty 作为扣减数量

        if (remainingQty <= 0) {
            throw new RuntimeException("出库明细的实际数量无效，无法扣减库存");
        }

        // 2. 根据物料编号、转入仓库（fromWarehouse）、账套编号查询库存
        QueryWrapper<WmsStockdetail> stockQueryWrapper = new QueryWrapper<>();
        stockQueryWrapper.eq("item_code", dump.getItemCode());
        stockQueryWrapper.eq("warehouse_code", dump.getFromWarehouse()); // 转入仓库
        stockQueryWrapper.eq("account_code", dump.getAccountCode());

        // 查询符合条件的所有库存记录
        List<WmsStockdetail> stockDetails = wmsStockdetailService.list(stockQueryWrapper);

        if (stockDetails == null || stockDetails.isEmpty()) {
            throw new RuntimeException("在仓库中未找到对应的库存，无法扣减库存");
        }

        // 3. 根据库存数量，选择合适的库存记录
        WmsStockdetail stockDetailToDeduct = null;
        for (WmsStockdetail stockDetail : stockDetails) {
            if (stockDetail.getQuantity() >= remainingQty) {
                stockDetailToDeduct = stockDetail;
                break;
            }
        }

        if (stockDetailToDeduct == null) {
            // 没有库存数量大于或等于所需数量的记录，无法扣减
            log.warn("物料 {} 在仓库 {} 中库存不足，需扣减数量：{}，可用库存数量不足", dump.getItemCode(), dump.getFromWarehouse(), remainingQty);
            throw new RuntimeException("库存不足，无法扣减所需的数量");
        }

        // 4. 扣减库存
        double stockQty = stockDetailToDeduct.getQuantity();
        if (stockQty >= remainingQty) {
            // 当前库存足够扣减
            stockDetailToDeduct.setQuantity(stockQty - remainingQty);
            stockDetailToDeduct.setUpdateTime(new Date());

            if (stockDetailToDeduct.getQuantity() <= 0) {
                // 库存扣减完毕，删除库存记录
                wmsStockdetailService.removeById(stockDetailToDeduct.getId());
            } else {
                // 更新库存记录
                wmsStockdetailService.updateById(stockDetailToDeduct);
            }

            log.info("物料 {} 库存扣减完成，扣减数量：{}", dump.getItemCode(), remainingQty);
        }
    }


    @Override
    public void syncProduceReturn() {
        // 1. 构建查询条件，只查询需同步的工单退料单
        QueryWrapper<WmsReceive> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_type", "GDTL");  // 工单退料
        queryWrapper.eq("bill_status", WmsConstant.ReceiveBillStatusEnum.RECEIVE_COMPLETE.getValue()); // 已完成
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue()); // 未同步成功
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());

        // 2. 查询符合条件的主表数据
        List<WmsReceive> receiveList = wmsReceiveService.list(queryWrapper);
        if (receiveList == null || receiveList.isEmpty()) {
            log.info("没有需要同步的工单退料单数据。");
            return;
        }

        // 3. 遍历查询到的工单退料单，逐条处理
        for (WmsReceive receive : receiveList) {
            try {
                // 3.1 查询明细
                List<WmsReceivedetail> details = wmsReceivedetailService.selectByMainId(receive.getId());
                if (details == null || details.isEmpty()) {
                    log.warn("工单退料单 [{}] 无明细数据，跳过同步。", receive.getBillNo());
                    // 继续处理下一条
                    continue;
                }

                // 3.2 组装请求数据
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", receive.getAccountCode()); // 账套代码

                // 过账日期
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = (receive.getPostDate() != null ? receive.getPostDate() : new Date());
                String docDate = dateFormat.format(postDate);
                requestJson.put("DocDate", docDate);

                // WMS/MES 来源单号
                requestJson.put("U_LYDH", receive.getBillNo());

                // 处理明细，分组聚合
                Map<String, JSONObject> linesMap = new HashMap<>();

                for (WmsReceivedetail detail : details) {
                    // 已同步的明细直接跳过
                    if (WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue().equals(detail.getLineState())) {
                        continue;
                    }

                    // 构建分组Key: BaseEntry_BaseLine
                    String baseEntry = receive.getWorkNo();
                    String baseLine = String.valueOf(detail.getLineNo());
                    String key = baseEntry + "_" + baseLine;

                    JSONObject lineJson = linesMap.get(key);
                    if (lineJson == null) {
                        // 若不存在该行，则创建
                        lineJson = new JSONObject();
                        // 基础单据编号(工单号)
                        if (baseEntry != null) {
                            try {
                                lineJson.put("BaseEntry", Integer.parseInt(baseEntry));
                            } catch (NumberFormatException e) {
                                log.warn("无法将 WorkNo [{}] 转为整数，置为null", baseEntry);
                                lineJson.put("BaseEntry", null);
                            }
                        } else {
                            lineJson.put("BaseEntry", null);
                        }

                        // 基础单据行号
                        lineJson.put("BaseLine", detail.getLineNo());
                        // 初始数量
                        lineJson.put("Quantity", detail.getInboundQty());

                        // 初始化批次数组
                        JSONArray obtnsArray = new JSONArray();
                        lineJson.put("OBTNs", obtnsArray);

                        // 放入 map
                        linesMap.put(key, lineJson);
                    } else {
                        // 若已存在，则累加数量
                        double currentQty = lineJson.getDoubleValue("Quantity");
                        lineJson.put("Quantity", currentQty + detail.getInboundQty());
                    }

                    // 每条明细对应的批次信息
                    JSONArray obtnsArray = lineJson.getJSONArray("OBTNs");
                    JSONObject obtnJson = new JSONObject();
                    obtnJson.put("DistNumber", detail.getBatchCode());
                    obtnJson.put("Quantity", detail.getInboundQty());
                    obtnsArray.add(obtnJson);
                }

                // 构建 Lines 数组
                JSONArray linesArray = new JSONArray();
                requestJson.put("Lines", linesArray);

                // 3.3 发送请求到 SAP
                String url = mainBaseUrl + ENDPOINT_GET_PRODUCTION_RETURN;
                JSONObject jsonObject = executeRequest(url, requestJson, "生产退料工单编号：" + receive.getWorkNo() + " 同步给SAP");

                // 3.4 根据返回更新主表和明细
                if (jsonObject.getBoolean("result")) {
                    // 同步成功
                    receive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                    receive.setUpdateTime(new Date());
                    wmsReceiveService.updateById(receive);

                    // 同时更新明细行状态为已同步
                    for (WmsReceivedetail detail : details) {
                        detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue());
                        detail.setUpdateTime(new Date());
                    }
                    wmsReceivedetailService.updateBatchById(details);

                    log.info("工单退料单 [{}] 同步成功。", receive.getBillNo());
                } else {
                    // 同步失败
                    String msg = jsonObject.getString("msg");
                    log.error("工单退料单 [{}] 同步失败，错误信息：{}", receive.getBillNo(), msg);

                    receive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                    receive.setUpdateTime(new Date());
                    wmsReceiveService.updateById(receive);
                }
            } catch (Exception e) {
                // 3.5 遇到任何异常，只影响当前单据
                log.error("工单退料单 [{}] 同步时出现异常：{}", (receive != null ? receive.getBillNo() : ""), e.getMessage(), e);

                // 标记当前单据为同步失败
                if (receive != null) {
                    receive.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                    receive.setUpdateTime(new Date());
                    wmsReceiveService.updateById(receive);
                }
                // 注意这里不再抛出异常，以免中断循环
            }
        }
    }


    @Override
    public void syncStockReceive() {
    }

    @Override
    public void syncStockSend() {
    }


    @Override
    public void syncProduceSend() {
        // 1. 构建查询条件：只查询需同步的"领料出库"单据 (LLCK)
        QueryWrapper<WmsSend> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
        queryWrapper.eq("bill_status", WmsConstant.SendBillStatusEnum.SEND_COMPLETE.getValue());
        queryWrapper.eq("bill_type", "LLCK");
        queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());

        // 2. 查询出库管理信息
        List<WmsSend> sendList = wmsSendService.list(queryWrapper);
        if (sendList == null || sendList.isEmpty()) {
            log.info("没有需要同步的出库管理数据。");
            return;
        }

        // 3. 遍历每条记录并进行同步处理
        for (WmsSend send : sendList) {
            try {
                // 3.1 组装请求数据
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", send.getAccountCode()); // 账套代码

                // 根据字段对接文档，构建请求数据
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = (send.getPostDate() != null ? send.getPostDate() : new Date());
                String docDate = dateFormat.format(postDate); // 过账日期
                requestJson.put("DocDate", docDate);
                requestJson.put("U_LYDH", send.getBillNo()); // WMS/MES来源单号

                // 3.2 查询明细数据
                List<WmsSenddetail> details = wmsSendDetailService.selectByMainId(send.getId());
                if (details == null || details.isEmpty()) {
                    log.warn("出库单 [{}] 无明细数据，跳过同步。", send.getBillNo());
                    continue; // 处理下一个
                }

                // 3.3 构建 Lines 数组
                JSONArray linesArray = new JSONArray();
                for (WmsSenddetail detail : details) {
                    // 根据行号(或明细ID)去查询对应的历史任务
                    QueryWrapper<WmsConttaskHis> taskQueryWrapper = new QueryWrapper<>();
                    taskQueryWrapper.eq("src_line_id", detail.getId());
                    List<WmsConttaskHis> taskList = wmsConttaskHisService.list(taskQueryWrapper);

                    if (taskList == null || taskList.isEmpty()) {
                        log.warn("明细 [{}] 无对应的历史任务，跳过。", detail.getLineNo());
                        continue;
                    }

                    // 按批次号分组
                    Map<String, List<WmsConttaskHis>> taskGroupByBatch = taskList.stream().collect(Collectors.groupingBy(WmsConttaskHis::getBatchCode));

                    JSONArray obtnsArray = new JSONArray();
                    double totalQuantity = 0.0;
                    for (Map.Entry<String, List<WmsConttaskHis>> entry : taskGroupByBatch.entrySet()) {
                        String batchCode = entry.getKey();
                        List<WmsConttaskHis> tasks = entry.getValue();

                        double batchQuantity = tasks.stream().mapToDouble(task -> {
                            // 计算出库数量
                            if ("1".equalsIgnoreCase(task.getSplitPallet())) {
                                return task.getSplitCount() != null ? task.getSplitCount() : 0.0;
                            } else {
                                return task.getTaskPacket() != null ? task.getTaskPacket() : 0.0;
                            }
                        }).sum();

                        totalQuantity += batchQuantity;

                        JSONObject obtnJson = new JSONObject();
                        obtnJson.put("DistNumber", batchCode);    // 批次号
                        obtnJson.put("Quantity", batchQuantity);   // 批次数量
                        obtnsArray.add(obtnJson);
                    }

                    // 构建行对象
                    JSONObject lineJson = new JSONObject();
                    lineJson.put("Quantity", totalQuantity);
                    // BaseEntry: 来源生产订单编号
                    lineJson.put("BaseEntry", send.getSerialNumber());
                    // BaseLine: 来源生产订单行号
                    lineJson.put("BaseLine", detail.getLineNo());
                    lineJson.put("OBTNs", obtnsArray);

                    linesArray.add(lineJson);
                }

                if (linesArray.isEmpty()) {
                    log.warn("出库单 [{}] 的明细均无有效数据，跳过同步。", send.getBillNo());
                    continue;
                }

                requestJson.put("Lines", linesArray);

                // 3.4 调用 SAP 接口
                String url = mainBaseUrl + ENDPOINT_GET_PRODUCTION_DELIVERY; // 领料出库对应接口
                JSONObject jsonObject = executeRequest(url, requestJson, "生产领料工单编号：" + send.getWorkNo() + " 同步给SAP");

                // 3.5 处理响应结果
                if (jsonObject.getBoolean("result")) {
                    // 同步成功
                    send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                    send.setUpdateTime(new Date());
                    wmsSendService.updateById(send);

                    log.info("生产领料出库单 [{}] 同步成功。", send.getBillNo());
                } else {
                    // 同步失败
                    String errorMsg = jsonObject.getString("msg");
                    log.error("生产领料出库单 [{}] 同步失败，错误信息：{}", send.getBillNo(), errorMsg);

                    send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                    send.setUpdateTime(new Date());
                    wmsSendService.updateById(send);
                }

            } catch (Exception e) {
                // 3.6 捕获当前记录的异常，保证不会中断整个循环
                log.error("生产领料出库单 [{}] 同步出现异常：{}", send.getBillNo(), e.getMessage(), e);

                send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                send.setUpdateTime(new Date());
                wmsSendService.updateById(send);
            }
        }
    }


    /**
     * 倒冲料同步SAP的定时作业，走生产领料同步接口
     */
    @Override
    public void syncBackflushSend() {
        // 1. 构建查询条件：只查询需同步的"倒冲料"单据 (BGLL)
        QueryWrapper<WmsSend> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
        queryWrapper.eq("bill_status", WmsConstant.SendBillStatusEnum.SEND_COMPLETE.getValue());
        queryWrapper.eq("bill_type", "BGLL");
        // queryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());

        // 2. 查询出库管理信息
        List<WmsSend> sendList = wmsSendService.list(queryWrapper);
        if (sendList == null || sendList.isEmpty()) {
            log.info("没有需要同步的倒冲料出库管理数据。");
            return;
        }

        // 3. 遍历每条记录并进行同步处理
        for (WmsSend send : sendList) {
            try {
                // 3.1 组装请求数据
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", send.getAccountCode()); // 账套代码

                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = (send.getPostDate() != null ? send.getPostDate() : new Date());
                String docDate = dateFormat.format(postDate); // 过账日期
                requestJson.put("DocDate", docDate);
                requestJson.put("U_LYDH", send.getBillNo()); // 倒冲料来源单号

                // 3.2 查询明细数据
                List<WmsSenddetail> details = wmsSendDetailService.selectByMainId(send.getId());
                if (details == null || details.isEmpty()) {
                    log.warn("倒冲料出库单 [{}] 无明细数据，跳过同步。", send.getBillNo());
                    continue;
                }

                // 3.3 构建 Lines 数组（倒冲料没有批次号，直接取明细单据的实发数量）
                JSONArray linesArray = new JSONArray();
                for (WmsSenddetail detail : details) {
                    // 判断该物料是否为仓库物料，如果不是，则跳过该明细
                    if (!wmsSpecMatchItemService.isWarehouseItem(detail.getItemNumber(), send.getAccountCode())) {
                        log.info("物料 [{}] 不是仓库物料，跳过该明细同步。", detail.getItemNumber());
                        continue;
                    }

                    JSONObject lineJson = new JSONObject();
                    // 根据实际需求替换为正确的字段，这里以 detail.getActQty() 为例
                    lineJson.put("Quantity", detail.getActQty());
                    // BaseEntry: 来源生产订单编号
                    lineJson.put("BaseEntry", send.getSerialNumber());
                    // BaseLine: 来源生产订单行号
                    lineJson.put("BaseLine", detail.getLineNo());
                    // 如果没有批次信息，倒冲料单据不需要 OBTNs

                    linesArray.add(lineJson);
                }

                // 如果经过过滤后没有有效的明细，则跳过该单据同步
                if (linesArray.isEmpty()) {
                    log.warn("倒冲料出库单 [{}] 的明细均非仓库物料，跳过同步。", send.getBillNo());
                    continue;
                }

                requestJson.put("Lines", linesArray);

                // 3.4 调用 SAP 接口
                String url = mainBaseUrl + ENDPOINT_GET_PRODUCTION_DELIVERY; // 领料出库对应接口
                JSONObject jsonObject = executeRequest(url, requestJson, "倒冲料生产领料单号：" + send.getWorkNo() + " 同步给SAP");

                // 3.5 处理响应结果
                if (jsonObject.getBoolean("result")) {
                    // 同步成功
                    send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                    send.setUpdateTime(new Date());
                    wmsSendService.updateById(send);

                    log.info("倒冲料出库单 [{}] 同步成功。", send.getBillNo());
                } else {
                    // 同步失败
                    String errorMsg = jsonObject.getString("msg");
                    log.error("倒冲料出库单 [{}] 同步失败，错误信息：{}", send.getBillNo(), errorMsg);

                    send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                    send.setUpdateTime(new Date());
                    wmsSendService.updateById(send);
                }

            } catch (Exception e) {
                // 3.6 捕获当前记录的异常，保证不会中断整个循环
                log.error("倒冲料出库单 [{}] 同步出现异常：{}", send.getBillNo(), e.getMessage(), e);

                send.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                send.setUpdateTime(new Date());
                wmsSendService.updateById(send);
            }
        }
    }


    @Override
    public void syncStockTransfer(WmsStockDump stockDump) throws SapSyncException {
        try {
            // 构建请求数据
            JSONObject requestJson = new JSONObject();
            requestJson.put("DBName", stockDump.getAccountCode()); // 账套代码

            // 根据字段对接文档，构建请求数据
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            // 如果 PostDate 为空，使用当前日期
            Date postDate = stockDump.getPostDate() != null ? stockDump.getPostDate() : new Date();
            String docDate = dateFormat.format(postDate);
            requestJson.put("DocDate", docDate);

            requestJson.put("U_LYDH", stockDump.getBillNo()); // WMS/MES来源单号

            // 构建 Lines 数组
            JSONArray linesArray = new JSONArray();

            JSONObject lineJson = new JSONObject();
            lineJson.put("ItemCode", stockDump.getItemCode()); // 物料编号
            lineJson.put("Dscription", stockDump.getItemName()); // 物料描述
            lineJson.put("Quantity", stockDump.getQty()); // 数量
            lineJson.put("FromWhsCod", stockDump.getToWarehouse()); // 转出仓库
            lineJson.put("WhsCod", stockDump.getFromWarehouse()); // 转入仓库

            // 构建批次信息 OBTNs 数组
            JSONArray obtnsArray = new JSONArray();
            if (stockDump.getBatchCode() != null && !stockDump.getBatchCode().isEmpty()) {
                JSONObject obtnJson = new JSONObject();
                obtnJson.put("DistNumber", stockDump.getBatchCode()); // 批次号
                obtnJson.put("Quantity", stockDump.getQty()); // 批次数量
                obtnsArray.add(obtnJson);
            }
            lineJson.put("OBTNs", obtnsArray);

            linesArray.add(lineJson);
            requestJson.put("Lines", linesArray);

            // 发送请求
            String url = mainBaseUrl + ENDPOINT_GET_STOCK_TRANSFER;
            JSONObject response = executeRequest(url, requestJson, "库存转储单据号: " + stockDump.getBillNo() + "同步给SAP");
//            JSONObject response = SapApiUtil.mockRequest(requestJson, "库存转储单据号: " + stockDump.getBillNo() + "同步给SAP", baseCommonService, null);

            // 处理响应结果
            if (response.getBoolean("result")) {
                // 同步成功
                log.info("库存转储单 [{}] 同步成功", stockDump.getBillNo());
            } else {
                // 同步失败，抛出异常
                String errorMsg = response.getString("msg");
                log.error("库存转储单 [{}] 同步失败，错误信息：{}", stockDump.getBillNo(), errorMsg);
                throw new SapSyncException("同步 SAP 失败，错误信息：" + errorMsg);
            }
        } catch (SapSyncException e) {
            throw e; // 重新抛出自定义异常
        } catch (Exception e) {
            log.error("库存转储单 [{}] 同步 SAP 时发生异常", stockDump.getBillNo(), e);
            throw new SapSyncException("同步 SAP 失败，异常信息：" + e.getMessage(), e);
        }
    }

    @Override
    public void batchSyncStockTransfer(List<WmsStockDump> stockDumpList) throws SapSyncException {
        if (stockDumpList == null || stockDumpList.isEmpty()) {
            log.info("没有需要同步的库存转储单数据");
            return;
        }

        // 1. 按账套代码分组，确保同一个请求中的所有记录都属于同一个账套
        Map<String, List<WmsStockDump>> accountGroupMap = stockDumpList.stream()
                .collect(Collectors.groupingBy(WmsStockDump::getAccountCode));

        // 2. 对每个账套分组进行处理
        for (Map.Entry<String, List<WmsStockDump>> entry : accountGroupMap.entrySet()) {
            String accountCode = entry.getKey();
            List<WmsStockDump> dumpList = entry.getValue();

            try {
                // 构建批量请求数据
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", accountCode); // 账套代码

                // 使用第一个记录的日期和单据号
                WmsStockDump firstDump = dumpList.get(0);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = firstDump.getPostDate() != null ? firstDump.getPostDate() : new Date();
                String docDate = dateFormat.format(postDate);
                requestJson.put("DocDate", docDate);
                requestJson.put("U_LYDH", firstDump.getBillNo()); // 使用第一个记录的单据号作为来源单号

                // 构建 Lines 数组
                JSONArray linesArray = new JSONArray();

                // 遍历每个库存转储单，添加到 Lines 数组
                for (WmsStockDump stockDump : dumpList) {
                    JSONObject lineJson = new JSONObject();
                    lineJson.put("ItemCode", stockDump.getItemCode()); // 物料编号
                    lineJson.put("Dscription", stockDump.getItemName()); // 物料描述
                    lineJson.put("Quantity", stockDump.getQty()); // 转储数量
                    lineJson.put("FromWhsCod", stockDump.getToWarehouse()); // 转出仓库
                    lineJson.put("WhsCod", stockDump.getFromWarehouse()); // 转入仓库

                    // 如果有批号，添加批号信息
                    if (stockDump.getBatchCode() != null && !stockDump.getBatchCode().isEmpty()) {
                        JSONArray obtnsArray = new JSONArray();
                        JSONObject obtnJson = new JSONObject();
                        obtnJson.put("DistNumber", stockDump.getBatchCode()); // 批号
                        obtnJson.put("Quantity", stockDump.getQty()); // 批次数量
                        obtnsArray.add(obtnJson);
                        lineJson.put("OBTNs", obtnsArray);
                    }

                    linesArray.add(lineJson);
                }

                requestJson.put("Lines", linesArray);

                // 发送请求
                String url = mainBaseUrl + ENDPOINT_GET_STOCK_TRANSFER;
                //获取本次同步的单据号
                String billNos = dumpList.stream().map(WmsStockDump::getBillNo).collect(Collectors.joining(","));
                JSONObject response = executeRequest(url, requestJson, "批量库存转储单同步给SAP，同步的单据号"+billNos);
//                JSONObject response = SapApiUtil.mockRequest(requestJson, "批量库存转储单同步给SAP，同步的单据号"+billNos, baseCommonService, null);

                // 处理响应结果
                if (!response.getBoolean("result")) {
                    // 同步失败，抛出异常
                    String errorMsg = response.getString("msg");
                    log.error("账套 [{}] 的库存转储单批量同步失败，错误信息：{}", accountCode, errorMsg);
                    throw new SapSyncException("同步 SAP 失败，错误信息：" + errorMsg);
                }

                // 同步成功
                log.info("账套 [{}] 的 {} 个库存转储单批量同步成功", accountCode, dumpList.size());
            } catch (SapSyncException e) {
                throw e; // 重新抛出自定义异常
            } catch (Exception e) {
                log.error("账套 [{}] 的库存转储单批量同步处理异常", accountCode, e);
                throw new SapSyncException("同步 SAP 失败，异常信息：" + e.getMessage(), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncBackflush() {
        // 查询待转储物料信息类型为 WGLL 的数据
        QueryWrapper<WmsSend> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bill_type", "WGLL");
        queryWrapper.ne("erp_sync", WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
        queryWrapper.eq("bill_status", WmsConstant.SendBillStatusEnum.SEND_COMPLETE.getValue());
        List<WmsSend> sendList = wmsSendService.list(queryWrapper);
        if (sendList.isEmpty()) {
            log.info("没有需要同步的物料倒冲数据。");
            return;
        }

        // 查询对应的出库明细，组装请求数据
        sendList.forEach(item -> {
            List<WmsSenddetail> wmsSenddetails = wmsSendDetailService.selectByMainId(item.getId());
            JSONObject requestJson = new JSONObject();
            requestJson.put("DBName", item.getAccountCode()); // 主表的 accountCode
            requestJson.put("BaseEntry", item.getWorkNo());   // 主表的 workNo
            requestJson.put("U_Z98_TYPE", "1");

            // 构建 Lines 数组
            JSONArray linesArray = new JSONArray();
            for (WmsSenddetail detail : wmsSenddetails) {
                // 检查 actQty 和 planQty 是否有差异
//                double difference = detail.getActQty() - detail.getPlanQty();
//                if (difference != 0) {
//                    // 有差异，调整库存
//                    adjustInventory(detail.getItemNumber(), detail.getWarehouseCode(), difference, item.getAccountCode());
//                }

                // 将明细添加到 Lines 数组
                JSONObject lineJson = new JSONObject();
                lineJson.put("ItemCode", detail.getItemNumber());  // 子表的 itemNumber
                lineJson.put("BaseLine", detail.getLineNo());      // 子表的 lineNo
                lineJson.put("Quantity", detail.getDisQty());      // 子表的 actQty
                linesArray.add(lineJson);
            }
            requestJson.put("Lines", linesArray);

            // 发送请求
            String url = mainBaseUrl + ENDPOINT_GET_BACK_FLUSH_DIFF;
            JSONObject jsonObject = executeRequest(url, requestJson, "倒冲差异同步SAP");

            // 处理响应结果
            if (jsonObject.getBoolean("result")) {
                // 同步成功，更新 erp_sync 状态为同步成功
                item.setErpSync(WmsConstant.ErpSyncEnum.SYNC_SUCCESS.getValue());
                item.setUpdateTime(new Date());
                wmsSendService.updateById(item);
            } else {
                // 同步失败，记录错误日志并更新 erp_sync 状态为同步失败
                log.error("物料 [{}] 同步失败，错误信息：{}", item.getId(), jsonObject.getString("msg"));
                item.setErpSync(WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
                item.setUpdateTime(new Date());
                wmsSendService.updateById(item);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void adjustInventory(String itemCode, String warehouseCode, double difference, String accountCode) {
        // 根据物料编码、仓库编码、账套编码查询库存明细
        QueryWrapper<WmsStockdetail> stockQueryWrapper = new QueryWrapper<>();
        stockQueryWrapper.eq("item_code", itemCode);
        stockQueryWrapper.eq("warehouse_code", warehouseCode);
        stockQueryWrapper.eq("account_code", accountCode);
        WmsStockdetail stockDetail = wmsStockdetailService.getOne(stockQueryWrapper);

        if (stockDetail == null) {
            throw new RuntimeException("未找到对应的库存记录，无法进行库存调整");
        }

        // 调整库存数量
        double newQuantity = stockDetail.getQuantity() - difference;

        if (newQuantity < 0) {
            throw new RuntimeException("库存不足，无法进行库存调整");
        }

        stockDetail.setQuantity(newQuantity);
        stockDetail.setUpdateTime(new Date());

        // 如果数量为 0，删除库存记录；否则更新库存记录
        if (newQuantity == 0) {
            wmsStockdetailService.removeById(stockDetail.getId());
        } else {
            wmsStockdetailService.updateById(stockDetail);
        }
    }
    @Override
    public JSONObject syncStockReceiveOrSend(WmsStockdReceiveSend receiveSend) throws SapSyncException {
        // 1. 根据 billType 决定是收货还是发货
        String billType = receiveSend.getBillType();
        String url = "";
        if ("1".equals(billType)) {
            url = mainBaseUrl + ENDPOINT_GET_STOCK_RECEIVE;
        } else if ("2".equals(billType)) {
            url = mainBaseUrl + ENDPOINT_GET_STOCK_SEND;
        }

        // 2. 组装请求数据
        JSONObject requestJson = buildRequestJson(receiveSend);

        // 3. 调用 SAP 接口
        JSONObject respJson = executeRequest(url, requestJson, "库存收/发同步给SAP" + receiveSend.getBillNo());
        // 这里的 executeRequest(...) 方法，是你自己封装的 HTTP 调用

       return respJson;
    }

    /**
     * 构造 SAP 请求报文
     */
    private JSONObject buildRequestJson(WmsStockdReceiveSend receiveSend) {
        // 举例：把前面 syncStockReceive / syncStockSend 重复的组装逻辑放在一起
        JSONObject requestJson = new JSONObject();
        requestJson.put("DBName", receiveSend.getAccountCode());

        // 设置过账日期
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date postDate = receiveSend.getPostDate() != null ? receiveSend.getPostDate() : new Date();
        String docDate = dateFormat.format(postDate);
        requestJson.put("DocDate", docDate);

        requestJson.put("U_Z98_TYPE", receiveSend.getTransactionType());  // 仓库事务类型
        requestJson.put("U_LYDH", receiveSend.getBillNo());               // 来源单号

        // 构建 Lines
        JSONArray linesArray = new JSONArray();
        JSONObject lineJson = new JSONObject();
        lineJson.put("ItemCode", receiveSend.getItemCode());
        lineJson.put("Quantity", receiveSend.getQuantity());
        lineJson.put("Price", receiveSend.getUnitPrice());
        lineJson.put("WarehouseCode", receiveSend.getWarehouseCode());

        // 构建批次信息
        JSONArray obtnsArray = new JSONArray();
        if (receiveSend.getBatchCode() != null && !receiveSend.getBatchCode().isEmpty()) {
            JSONObject obtnJson = new JSONObject();
            obtnJson.put("DistNumber", receiveSend.getBatchCode());
            obtnJson.put("Quantity", receiveSend.getQuantity());
            obtnsArray.add(obtnJson);
        }
        lineJson.put("OBTNs", obtnsArray);

        linesArray.add(lineJson);
        requestJson.put("Lines", linesArray);

        return requestJson;
    }

    @Override
    public JSONObject batchSyncStockReceiveOrSend(List<WmsStockdReceiveSend> receiveSendList) throws SapSyncException {
        if (receiveSendList == null || receiveSendList.isEmpty()) {
            log.info("没有需要同步的库存收发单数据");
            JSONObject result = new JSONObject();
            result.put("result", true);
            result.put("msg", "没有需要同步的数据");
            return result;
        }

        // 1. 按账套代码、单据类型和事务类型分组，确保同一个请求中的所有记录都属于同一个账套、类型和事务类型
        Map<String, List<WmsStockdReceiveSend>> groupMap = receiveSendList.stream()
                .collect(Collectors.groupingBy(item -> {
                    String accountCode = item.getAccountCode() != null ? item.getAccountCode() : "DEFAULT";
                    String billType = item.getBillType() != null ? item.getBillType() : "UNKNOWN";
                    String transactionType = item.getTransactionType() != null ? item.getTransactionType() : "DEFAULT";
                    return accountCode + "_" + billType + "_" + transactionType;
                }));

        // 记录所有成功和失败的信息
        List<String> successMessages = new ArrayList<>();
        List<String> errorMessages = new ArrayList<>();

        // 2. 分组处理每个账套、单据类型和事务类型
        for (Map.Entry<String, List<WmsStockdReceiveSend>> entry : groupMap.entrySet()) {
            String groupKey = entry.getKey();
            List<WmsStockdReceiveSend> groupList = entry.getValue();

            String[] keyParts = groupKey.split("_");
            String accountCode = keyParts[0];
            String billType = keyParts[1];
            String transactionType = keyParts.length > 2 ? keyParts[2] : "DEFAULT";

            try {
                // 构建请求数据
                JSONObject requestJson = buildBatchRequestJson(groupList, accountCode, billType, transactionType);

                // 确定请求URL
                String url = "";
                if ("1".equals(billType)) {
                    url = mainBaseUrl + ENDPOINT_GET_STOCK_RECEIVE;
                } else if ("2".equals(billType)) {
                    url = mainBaseUrl + ENDPOINT_GET_STOCK_SEND;
                } else {
                    throw new SapSyncException("不支持的单据类型: " + billType);
                }

                // 发送请求
                String billNos = groupList.stream().map(WmsStockdReceiveSend::getBillNo).collect(Collectors.joining(","));
//                JSONObject response = executeRequest(url, requestJson, "批量库存收发单同步给SAP，同步的单据号: " + billNos);
                JSONObject response = SapApiUtil.mockRequest(requestJson, "批量库存收发单同步给SAP，同步的单据号: " + billNos, baseCommonService, null);

                // 处理响应结果
                if (response.getBoolean("result")) {
                    successMessages.add("账套 [" + accountCode + "] 单据类型 [" + billType + "] 事务类型 [" + transactionType + "] 同步成功，单据号: " + billNos);
                    log.info("账套 [{}] 单据类型 [{}] 事务类型 [{}] 的库存收发单批量同步成功", accountCode, billType, transactionType);
                } else {
                    String errorMsg = response.getString("msg");
                    errorMessages.add("账套 [" + accountCode + "] 单据类型 [" + billType + "] 事务类型 [" + transactionType + "] 同步失败: " + errorMsg);
                    log.error("账套 [{}] 单据类型 [{}] 事务类型 [{}] 的库存收发单批量同步失败，错误信息：{}", accountCode, billType, transactionType, errorMsg);
                }

            } catch (Exception e) {
                String errorMsg = "账套 [" + accountCode + "] 单据类型 [" + billType + "] 事务类型 [" + transactionType + "] 同步异常: " + e.getMessage();
                errorMessages.add(errorMsg);
                log.error(errorMsg, e);
            }
        }

        // 3. 汇总结果
        JSONObject result = new JSONObject();
        if (errorMessages.isEmpty()) {
            result.put("result", true);
            result.put("msg", "批量同步成功: " + String.join("; ", successMessages));
        } else {
            result.put("result", false);
            result.put("msg", "批量同步部分失败: " + String.join("; ", errorMessages));
            if (!successMessages.isEmpty()) {
                result.put("successMsg", String.join("; ", successMessages));
            }
        }

        return result;
    }

    /**
     * 构建批量请求的JSON数据
     */
    private JSONObject buildBatchRequestJson(List<WmsStockdReceiveSend> receiveSendList, String accountCode, String billType, String transactionType) {
        JSONObject requestJson = new JSONObject();
        requestJson.put("DBName", accountCode);

        // 设置过账日期（使用第一个记录的过账日期，或当前日期）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date postDate = receiveSendList.get(0).getPostDate() != null ?
                       receiveSendList.get(0).getPostDate() : new Date();
        String docDate = dateFormat.format(postDate);
        requestJson.put("DocDate", docDate);

        // 设置事务类型（使用分组的事务类型）
        if (transactionType != null && !"DEFAULT".equals(transactionType)) {
            requestJson.put("U_Z98_TYPE", transactionType);
        }

        // 生成批量来源单号
        String batchBillNos = receiveSendList.stream()
                .map(WmsStockdReceiveSend::getBillNo)
                .collect(Collectors.joining(","));
        requestJson.put("U_LYDH", "BATCH_" + System.currentTimeMillis() + "_" + batchBillNos);

        // 构建 Lines 数组
        JSONArray linesArray = new JSONArray();
        for (WmsStockdReceiveSend receiveSend : receiveSendList) {
            JSONObject lineJson = new JSONObject();
            lineJson.put("ItemCode", receiveSend.getItemCode());
            lineJson.put("Quantity", receiveSend.getQuantity());
            lineJson.put("Price", receiveSend.getUnitPrice());
            lineJson.put("WarehouseCode", receiveSend.getWarehouseCode());

            // 构建批次信息
            JSONArray obtnsArray = new JSONArray();
            if (receiveSend.getBatchCode() != null && !receiveSend.getBatchCode().isEmpty()) {
                JSONObject obtnJson = new JSONObject();
                obtnJson.put("DistNumber", receiveSend.getBatchCode());
                obtnJson.put("Quantity", receiveSend.getQuantity());
                obtnsArray.add(obtnJson);
            }
            lineJson.put("OBTNs", obtnsArray);

            linesArray.add(lineJson);
        }
        requestJson.put("Lines", linesArray);

        return requestJson;
    }

    @Override
    public void getSyncError() {
        LocalDateTime startOfYesterday = LocalDate.now().minusDays(1).atStartOfDay();
        LocalDateTime endOfYesterday = startOfYesterday.plusDays(1).minusNanos(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startOfYesterdayStr = startOfYesterday.format(formatter);
        String endOfYesterdayStr = endOfYesterday.format(formatter);

        // 查询采购入库同步失败的记录
        QueryWrapper<WmsReceive> wmsReceiveQueryWrapper = new QueryWrapper<>();
        wmsReceiveQueryWrapper.eq("bill_type", "CGRK");
        wmsReceiveQueryWrapper.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        wmsReceiveQueryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        wmsReceiveQueryWrapper.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsReceive> receiveList = wmsReceiveService.list(wmsReceiveQueryWrapper);

        // 查询采购退货同步失败的记录
        QueryWrapper<WmsPurchaseReturn> wmsPurchaseReturnQueryWrapper = new QueryWrapper<>();
        wmsPurchaseReturnQueryWrapper.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        wmsPurchaseReturnQueryWrapper.eq("bill_status", WmsConstant.PurchaseReturnBillStatusEnum.COMPLETE.getValue());
        wmsPurchaseReturnQueryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        wmsPurchaseReturnQueryWrapper.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsPurchaseReturn> purchaseReturnList = wmsPurchaseReturnService.list(wmsPurchaseReturnQueryWrapper);

        // 查询销售出库同步失败记录
        QueryWrapper<WmsSend> wmsSendQueryWrapper = new QueryWrapper<>();
        wmsSendQueryWrapper.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        wmsSendQueryWrapper.eq("bill_status", WmsConstant.SendBillStatusEnum.SEND_COMPLETE.getValue());
        wmsSendQueryWrapper.eq("bill_type", "XSCK");
        wmsSendQueryWrapper.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        wmsSendQueryWrapper.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsSend> sendList = wmsSendService.list(wmsSendQueryWrapper);

        // 查询销售退货同步失败记录
        QueryWrapper<WmsReceive> wmsReceiveQueryWrapper1 = new QueryWrapper<>();
        wmsReceiveQueryWrapper1.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        wmsReceiveQueryWrapper1.eq("bill_status", WmsConstant.ReceiveBillStatusEnum.RECEIVE_COMPLETE.getValue());
        wmsReceiveQueryWrapper1.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        wmsReceiveQueryWrapper1.eq("bill_type", "XSTHRK");
        wmsReceiveQueryWrapper1.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsReceive> receiveList1 = wmsReceiveService.list(wmsReceiveQueryWrapper1);

        // 查询生产入库和生产暂存同步失败记录
        QueryWrapper<WmsReceive> wmsReceiveQueryWrapper2 = new QueryWrapper<>();
        wmsReceiveQueryWrapper2.in("bill_type", "SCRK", "SCZC");
        wmsReceiveQueryWrapper2.eq("bill_status", WmsConstant.ReceiveBillStatusEnum.RECEIVE_COMPLETE.getValue());
        wmsReceiveQueryWrapper2.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        wmsReceiveQueryWrapper2.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        wmsReceiveQueryWrapper2.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsReceive> receiveList2 = wmsReceiveService.list(wmsReceiveQueryWrapper2);

        // 查询工单退料同步失败记录
        QueryWrapper<WmsReceive> wmsReceiveQueryWrapper3 = new QueryWrapper<>();
        wmsReceiveQueryWrapper3.eq("bill_type", "GDTL");
        wmsReceiveQueryWrapper3.eq("bill_status", WmsConstant.ReceiveBillStatusEnum.RECEIVE_COMPLETE.getValue());
        wmsReceiveQueryWrapper3.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        wmsReceiveQueryWrapper3.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        wmsReceiveQueryWrapper3.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsReceive> receiveList3 = wmsReceiveService.list(wmsReceiveQueryWrapper3);

        // 查询生产领料同步失败记录
        QueryWrapper<WmsSend> wmsSendQueryWrapper1 = new QueryWrapper<>();
        wmsSendQueryWrapper1.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        wmsSendQueryWrapper1.eq("bill_status", WmsConstant.SendBillStatusEnum.SEND_COMPLETE.getValue());
        wmsSendQueryWrapper1.eq("bill_type", "LLCK");
        wmsSendQueryWrapper1.eq("from_erp", WmsConstant.FromErpEnum.YES.getValue());
        wmsSendQueryWrapper1.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsSend> sendList1 = wmsSendService.list(wmsSendQueryWrapper1);

        // 查询库存收发同步失败记录
        QueryWrapper<WmsStockdReceiveSend> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("bill_type", WmsConstant.StockdReceiveSendBillTypeEnum.STOCK_IN.getValue(), WmsConstant.StockdReceiveSendBillTypeEnum.STOCK_OUT.getValue());
        queryWrapper.eq("erp_sync", WmsConstant.ErpSyncEnum.SYNC_FAIL.getValue());
        queryWrapper.eq("approve_status", "1");
        queryWrapper.between("create_time", startOfYesterdayStr, endOfYesterdayStr); // 只查询昨天的记录
        List<WmsStockdReceiveSend> stockdReceiveSendList = wmsStockdReceiveSendMapper.selectList(queryWrapper);

        // 汇总所有失败记录的单据编号
        StringBuilder messageContent = new StringBuilder();

        appendSyncErrorInfo(messageContent, receiveList, "采购入库");
        appendSyncErrorInfo(messageContent, purchaseReturnList, "采购退货");
        appendSyncErrorInfo(messageContent, sendList, "销售出库");
        appendSyncErrorInfo(messageContent, receiveList1, "销售退货");
        appendSyncErrorInfo(messageContent, receiveList2, "生产入库/暂存");
        appendSyncErrorInfo(messageContent, receiveList3, "工单退料");
        appendSyncErrorInfo(messageContent, sendList1, "生产领料");
        appendSyncErrorInfo(messageContent, stockdReceiveSendList, "库存收发");

        // 如果没有失败记录，可以设置一个默认提示
        if (messageContent.length() == 0) {
            messageContent.append("昨天没有同步失败的记录。");
        }

        List<ComboModel> comboModels = sysBaseAPI.queryAllUserBackCombo();
        //获取comboModels中的username，并组成以逗号分隔的字符串
        String userNames = comboModels.stream().map(ComboModel::getUsername).collect(Collectors.joining(","));
        // 创建消息DTO
        MessageDTO message = new MessageDTO();
        message.setFromUser("system"); // 发送者可以设置为系统用户
        message.setToAll(true); // 发送给所有人
        message.setToUser(userNames);
        message.setTitle("昨天同步失败的记录");
        message.setContent(messageContent.toString());
        message.setCategory(CommonConstant.MSG_CATEGORY_2); // 系统消息
        message.setType("system"); // 消息类型

        // 发送消息
        sysBaseAPI.sendSysAnnouncement(message);
    }

    // 辅助方法，将失败记录的单据编号追加到消息内容中
    private void appendSyncErrorInfo(StringBuilder messageContent, List<?> failedList, String recordType) {
        if (failedList != null && !failedList.isEmpty()) {
            messageContent.append(recordType).append("同步失败的单据编号:\n");
            for (Object record : failedList) {
                // 假设每个记录类有getBillNo()方法来获取单据编号
                String billNo = getBillNo(record); // 获取单据编号
                messageContent.append(billNo).append("\n");
            }
            messageContent.append("\n");
        }
    }

    // 辅助方法：根据不同的记录类型，提取单据编号
    private String getBillNo(Object record) {
        if (record instanceof WmsReceive) {
            return ((WmsReceive) record).getBillNo(); // 假设WmsReceive有getBillNo()方法
        } else if (record instanceof WmsPurchaseReturn) {
            return ((WmsPurchaseReturn) record).getBillNo(); // 假设WmsPurchaseReturn有getBillNo()方法
        } else if (record instanceof WmsSend) {
            return ((WmsSend) record).getBillNo(); // 假设WmsSend有getBillNo()方法
        } else if (record instanceof WmsStockdReceiveSend) {
            return ((WmsStockdReceiveSend) record).getBillNo(); // 假设WmsStockdReceiveSend有getBillNo()方法
        }
        return "未知单据";
    }

    /**
     * 单据类型与处理方法的映射
     */
    private final Map<String, Function<String[], JSONObject>> syncHandlers = new HashMap<String, Function<String[], JSONObject>>() {{
        put("CGRK", ids -> {
            try {
                return manualSyncReceive(ids);
            } catch (SapSyncException e) {
                log.error("手动同步采购入库单失败", e);
                JSONObject result = new JSONObject();
                result.put("success", false);
                result.put("message", e.getMessage());
                return result;
            }
        });
        // 可以继续添加其他单据类型的处理方法
    }};

    @Override
    public JSONObject manualSync(String billType, String[] ids) throws SapSyncException {
        // 根据单据类型获取对应的处理方法
        Function<String[], JSONObject> handler = syncHandlers.get(billType);
        if (handler == null) {
            throw new SapSyncException("不支持的单据类型: " + billType);
        }

        // 调用对应的处理方法
        return handler.apply(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject manualSyncReceive(String[] ids) throws SapSyncException {
        JSONObject result = new JSONObject();
        List<String> successIds = new ArrayList<>();
        List<String> failIds = new ArrayList<>();
        List<String> errorMsgs = new ArrayList<>();

        if (ids == null || ids.length == 0) {
            result.put("success", false);
            result.put("message", "未选择需要同步的单据");
            return result;
        }

        for (String billId : ids) {
            try {
                // 查询收货单主表
                WmsReceive receive = wmsReceiveService.getById(billId);
                if (receive == null) {
                    failIds.add(billId);
                    errorMsgs.add("单据ID: " + billId + ", 错误: 未找到对应的采购入库单");
                    continue;
                }

                // 只处理采购入库单
                if (!"CGRK".equals(receive.getBillType())) {
                    failIds.add(billId);
                    errorMsgs.add("单据ID: " + billId + ", 错误: 单据类型不是采购入库单");
                    continue;
                }

                // 查询收货单明细
                QueryWrapper<WmsReceivedetail> detailWrapper = new QueryWrapper<>();
                detailWrapper.eq("bill_id", billId);
                detailWrapper.in("line_state", Arrays.asList(
                    WmsConstant.ReceiveDetailLineStateEnum.INBOUND_COMPLETE.getValue(),
                    WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_FAIL.getValue()
                ));
                List<WmsReceivedetail> wmsReceivedetails = wmsReceivedetailService.list(detailWrapper);

                if (wmsReceivedetails == null || wmsReceivedetails.isEmpty()) {
                    failIds.add(billId);
                    errorMsgs.add("单据ID: " + billId + ", 错误: 未找到状态为入库完成的明细");
                    continue;
                }

                // 组装请求数据
                JSONObject requestJson = new JSONObject();
                requestJson.put("DBName", receive.getAccountCode());        // 账套代码
                requestJson.put("CardCode", receive.getSupplierCode());     // 供应商代码
                requestJson.put("CardName", receive.getSupplierName());     // 供应商名称

                // 格式化日期为字符串
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date postDate = receive.getPostDate() != null ? receive.getPostDate() : new Date();
                String docDate = dateFormat.format(postDate);     // 过账日期
                Date dueDate = receive.getDeliveryDate() != null ? receive.getDeliveryDate() : new Date();
                String docDueDate = dateFormat.format(dueDate);   // 到期日
                requestJson.put("DocDate", dateFormat.format(new Date()));
                requestJson.put("DocDueDate", docDueDate);

                // 给来源单号加个随机数，防止重复
                Random random = new Random();
                int randomNumber = random.nextInt(10000); // 生成 0 到 9999 的随机数
                requestJson.put("U_LYDH", receive.getBillNo() + randomNumber);

                // 构建 Lines 数组（可能会有多行）
                Map<String, JSONObject> linesMap = new HashMap<>();
                SimpleDateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");

                for (WmsReceivedetail detail : wmsReceivedetails) {
                    // 按物料、基础单据编号及行号等信息分组，避免在 SAP 中出现多行合并
                    String key = detail.getItemNumber() + "_" + detail.getItemName() + "_" + receive.getSerialNumber() + "_" + detail.getLineNo();

                    JSONObject lineJson = linesMap.get(key);
                    if (lineJson == null) {
                        // 不存在则新建
                        lineJson = new JSONObject();
                        lineJson.put("ItemCode", detail.getItemNumber());  // 物料代码
                        lineJson.put("ItemName", detail.getItemName());    // 物料名称
                        lineJson.put("Quantity", detail.getInboundQty());  // 数量

                        try {
                            lineJson.put("BaseEntry", receive.getSerialNumber()); // 基础单据编号
                        } catch (NumberFormatException e) {
                            log.error("无法解析的 SerialNumber：{}", receive.getSerialNumber());
                            lineJson.put("BaseEntry", null);
                        }

                        lineJson.put("BaseLine", detail.getLineNo()); // 基础单据行号

                        // 初始化批次列表
                        JSONArray obtnsArray = new JSONArray();
                        lineJson.put("OBTNs", obtnsArray);

                        linesMap.put(key, lineJson);
                    } else {
                        // 如果已存在则累加数量
                        double currentQuantity = lineJson.getDoubleValue("Quantity");
                        lineJson.put("Quantity", currentQuantity + detail.getInboundQty());
                    }

                    // 构建批次信息
                    JSONArray obtnsArray = lineJson.getJSONArray("OBTNs");
                    JSONObject obtnJson = new JSONObject();
                    obtnJson.put("DistNumber", detail.getBatchCode()); // 批次号
                    obtnJson.put("Quantity", detail.getInboundQty());  // 数量
                    String mnfDate = dateFormat1.format(detail.getProductDate() == null ? new Date() : detail.getProductDate()); // 生产日期
                    obtnJson.put("mnfdate", mnfDate);
                    String expDate = dateFormat1.format(detail.getExpirationDate() == null ? new Date() : detail.getExpirationDate()); // 到期日期
                    obtnJson.put("ExpDate", expDate);
                    obtnsArray.add(obtnJson);
                }

                // 若没有可同步的行，直接跳过
                if (linesMap.isEmpty()) {
                    failIds.add(billId);
                    errorMsgs.add("单据ID: " + billId + ", 错误: 没有可同步的明细数据");
                    continue;
                }

                // 组装到 Lines
                JSONArray linesArray = new JSONArray();
                linesArray.addAll(linesMap.values());
                requestJson.put("Lines", linesArray);

                log.info("手动同步采购入库单请求数据: {}", requestJson.toJSONString());

                // 发送请求
                String url = mainBaseUrl + ENDPOINT_GET_PURCHASE_RECEIVE;
                JSONObject jsonObject = executeRequest(url, requestJson, "手动同步采购入库单据标识：" + receive.getSerialNumber() + "同步给SAP");
                // 判断同步结果
                if (jsonObject.getBoolean("result")) {
                    // 同步成功 -> 更新明细行状态为已同步
                    wmsReceivedetails.forEach(detail -> {
                        detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_COMPLETE.getValue());
                        detail.setUpdateTime(new Date());
                    });
                    wmsReceivedetailService.updateBatchById(wmsReceivedetails);
                    successIds.add(billId);
                } else {
                    // 同步失败 -> 更新明细行状态为同步失败
                    wmsReceivedetails.forEach(detail -> {
                        detail.setLineState(WmsConstant.ReceiveDetailLineStateEnum.INBOUND_SYNC_FAIL.getValue());
                        detail.setUpdateTime(new Date());
                    });
                    wmsReceivedetailService.updateBatchById(wmsReceivedetails);
                    failIds.add(billId);
                    errorMsgs.add("单据ID: " + billId + ", 错误: " + jsonObject.getString("msg"));
                }
                // 无论同步是否成功，都解禁库存
                wmsReceivedetails.forEach(detail -> {
                    // 查询对应的库存明细记录
                    QueryWrapper<WmsStockdetail> stockDetailWrapper = new QueryWrapper<>();
                    stockDetailWrapper.eq("bill_dtl_id", detail.getId())
                            .eq("item_code", detail.getItemNumber());

                    List<WmsStockdetail> stockList = wmsStockdetailService.list(stockDetailWrapper);

                    if (!stockList.isEmpty()) {
                        for (WmsStockdetail stock : stockList) {
                            // 检查库存是否已经解禁
                            if (!"0".equals(stock.getIsForbid())) {
                                stock.setIsForbid("0");  // 0表示非禁用
                                stock.setUpdateTime(new Date());
                                wmsStockdetailService.updateById(stock);
                                log.info("收货单明细ID [{}] 物料 [{}] 的库存已解禁",
                                        detail.getId(), detail.getItemNumber());
                            } else {
                                log.info("收货单明细ID [{}] 物料 [{}] 的库存已经是非禁用状态，无需解禁",
                                        detail.getId(), detail.getItemNumber());
                            }
                        }
                    } else {
                        log.warn("收货单明细ID [{}] 物料 [{}] 没有关联的库存记录",
                                detail.getId(), detail.getItemNumber());
                    }
                });
            } catch (Exception e) {
                log.error("手动同步采购入库单失败, 单据ID: " + billId, e);
                failIds.add(billId);
                errorMsgs.add("单据ID: " + billId + ", 错误: " + e.getMessage());
            }
        }

        result.put("success", !successIds.isEmpty());
        result.put("successIds", successIds);
        result.put("failIds", failIds);
        result.put("errorMsgs", errorMsgs);
        result.put("totalCount", ids.length);
        result.put("successCount", successIds.size());
        result.put("failCount", failIds.size());

        return result;
    }
}
