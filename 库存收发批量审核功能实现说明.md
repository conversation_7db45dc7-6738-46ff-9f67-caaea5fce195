# 库存收发批量审核功能实现说明

## 功能概述

按照库存转储单的方式，完成了库存收发的 `handleAuditBatch` 批量审核功能开发。当一次性选中多个库存收发单时，系统会将所有选中的物料放进一个SAP请求的Lines数组中，如果有批号，则放进Lines下的OBTNs数组中。

## 核心实现

### 1. 接口层面

#### ISapApiService 接口新增方法
```java
/**
 * 批量同步库存收发单到SAP
 * @param receiveSendList 库存收发单列表
 * @return 同步结果
 * @throws SapSyncException 同步异常
 */
JSONObject batchSyncStockReceiveOrSend(List<WmsStockdReceiveSend> receiveSendList) throws SapSyncException;
```

#### IWmsReceiveSendHandlerService 接口新增方法
```java
/**
 * 批量处理收发单审核：
 * 1. 批量处理库存操作
 * 2. 批量同步 SAP
 * 3. 更新单据状态
 *
 * @param receiveSendList 收发单列表
 * @return 处理结果
 */
JSONObject handleAuditDataBatch(List<WmsStockdReceiveSend> receiveSendList);
```

### 2. 服务层实现

#### SapApiServiceImpl 批量同步实现
- **分组处理**: 按账套代码和单据类型分组，确保同一请求中的记录属于同一账套和类型
- **Lines构建**: 将所有物料信息放入Lines数组
- **OBTNs处理**: 有批号的物料，批号信息放入OBTNs数组
- **错误处理**: 分组处理，部分失败不影响其他组的处理

#### WmsReceiveSendHandlerServiceImpl 批量处理实现
- **库存操作**: 先处理所有记录的库存增减操作
- **SAP同步**: 库存操作成功后，批量调用SAP同步
- **状态更新**: 根据同步结果更新所有记录的状态
- **事务管理**: 使用@Transactional确保数据一致性

#### WmsStockdReceiveSendServiceImpl 主服务实现
- **数据查询**: 根据IDs批量查询库存收发记录
- **重复过滤**: 过滤已处理成功的记录，避免重复处理
- **批量调用**: 调用批量处理服务
- **结果处理**: 批量更新数据库记录状态

### 3. 控制器层

#### WmsStockdReceiveSendController 新增接口
```java
/**
 * 通过ids进行批量审核
 * @param ids
 * @return
 */
@AutoLog(value = "WMS库存收发-通过ids进行批量审核")
@ApiOperation(value="WMS库存收发-通过ids进行批量审核", notes="WMS库存收发-通过ids进行批量审核")
@GetMapping(value = "/handleAuditBatch")
public Result<String> handleAuditBatch(@RequestParam(name="ids",required=true) String ids) {
    wmsStockdReceiveSendService.handleAuditBatch(ids);
    return Result.OK("批量审核成功!");
}
```

## SAP请求数据结构

### 批量请求格式
```json
{
  "DBName": "账套代码",
  "DocDate": "2024-12-28",
  "U_Z98_TYPE": "事务类型",
  "U_LYDH": "BATCH_时间戳_单据号列表",
  "Lines": [
    {
      "ItemCode": "物料编号",
      "Quantity": 数量,
      "Price": 单价,
      "WarehouseCode": "仓库代码",
      "OBTNs": [
        {
          "DistNumber": "批号",
          "Quantity": 批次数量
        }
      ]
    }
  ]
}
```

### Lines数组说明
- 每个物料对应Lines数组中的一个元素
- 包含物料基本信息：编号、数量、价格、仓库等
- 如果有批号，在OBTNs数组中包含批号信息

### OBTNs数组说明
- 只有当物料有批号时才包含此数组
- 每个批号对应一个OBTNs元素
- 包含批号和对应的数量

## 处理流程

### 1. 批量审核流程
```
用户选择多个库存收发单
    ↓
调用 handleAuditBatch(ids)
    ↓
查询并过滤需要处理的记录
    ↓
调用 handleAuditDataBatch(list)
    ↓
逐个处理库存操作（不同步SAP）
    ↓
批量同步SAP（按账套和单据类型分组）
    ↓
更新所有记录状态
    ↓
返回处理结果
```

### 2. SAP同步优化
- **单个处理**: 立即同步SAP
- **批量处理**: 最后统一同步SAP，提高效率
- **分组同步**: 按账套和单据类型分组，避免混合请求

### 3. 错误处理机制
- **库存操作失败**: 记录错误，继续处理其他记录
- **SAP同步失败**: 更新失败状态，不影响已处理的库存
- **部分失败**: 返回详细的成功和失败信息

## 关键特性

### 1. 批量优化
- 减少SAP接口调用次数
- 提高处理效率
- 降低网络开销

### 2. 数据一致性
- 事务管理确保数据完整性
- 状态同步保证业务一致性
- 错误回滚机制

### 3. 容错处理
- 单个记录失败不影响整体
- 详细的错误信息记录
- 支持重试机制

### 4. 性能优化
- 批量数据库操作
- 分组处理减少重复工作
- 内存使用优化

## 使用示例

### 前端调用
```javascript
// 批量审核库存收发单
const ids = "id1,id2,id3";
axios.get(`/admin/wmsStockdReceiveSend/handleAuditBatch?ids=${ids}`)
  .then(response => {
    console.log('批量审核成功:', response.data);
  })
  .catch(error => {
    console.error('批量审核失败:', error);
  });
```

### 后端测试
```java
// 创建测试数据
List<WmsStockdReceiveSend> testList = createTestData();

// 调用批量处理
JSONObject result = receiveSendHandlerService.handleAuditDataBatch(testList);

// 检查结果
if (result.getBoolean("result")) {
    System.out.println("批量处理成功");
} else {
    System.out.println("批量处理失败: " + result.getString("msg"));
}
```

## 总结

本次实现完全按照库存转储单的模式，实现了库存收发的批量审核功能。主要特点：

1. **统一的数据结构**: Lines数组包含所有物料，OBTNs数组包含批号信息
2. **高效的批量处理**: 减少SAP接口调用，提高处理效率
3. **完善的错误处理**: 部分失败不影响整体，详细的错误信息
4. **良好的扩展性**: 易于添加新的单据类型和处理逻辑

该实现满足了用户的需求，提供了高效、可靠的批量审核功能。
