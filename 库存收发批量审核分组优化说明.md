# 库存收发批量审核分组优化说明

## 优化背景

原来的代码在设置事务类型时只是使用第一个记录的事务类型，这会导致问题：
- 如果选中的多个单据有不同的事务类型，会被强制使用第一个的事务类型
- 这可能导致SAP同步时数据不正确

## 优化方案

### 1. 分组逻辑优化

**原来的分组逻辑**：
```java
// 只按账套代码和单据类型分组
Map<String, List<WmsStockdReceiveSend>> groupMap = receiveSendList.stream()
    .collect(Collectors.groupingBy(item ->
        item.getAccountCode() + "_" + item.getBillType()));
```

**优化后的分组逻辑**：
```java
// 按账套代码、单据类型和事务类型分组，使用"-"分隔符避免账套代码中的下划线冲突
Map<String, List<WmsStockdReceiveSend>> groupMap = receiveSendList.stream()
    .collect(Collectors.groupingBy(item -> {
        String accountCode = item.getAccountCode() != null ? item.getAccountCode() : "DEFAULT";
        String billType = item.getBillType() != null ? item.getBillType() : "UNKNOWN";
        String transactionType = item.getTransactionType() != null ? item.getTransactionType() : "DEFAULT";
        return accountCode + "-" + billType + "-" + transactionType;
    }));
```

### 分隔符选择说明

**为什么使用 `-` 而不是 `_`？**

1. **避免冲突**：账套代码可能包含下划线，如 `COMPANY_001`
2. **解析准确**：使用 `-` 可以准确分割各个部分
3. **示例对比**：
   - 账套代码：`COMPANY_001`
   - 使用 `_` 分隔：`COMPANY_001_1_RECEIVE` → split("_") = ["COMPANY", "001", "1", "RECEIVE"] ❌
   - 使用 `-` 分隔：`COMPANY_001-1-RECEIVE` → split("-") = ["COMPANY_001", "1", "RECEIVE"] ✅

### 2. 事务类型处理优化

**原来的处理方式**：
```java
// 使用第一个记录的事务类型（有问题）
String transactionType = receiveSendList.get(0).getTransactionType();
if (transactionType != null) {
    requestJson.put("U_Z98_TYPE", transactionType);
}
```

**优化后的处理方式**：
```java
// 使用分组的事务类型（正确）
if (transactionType != null && !"DEFAULT".equals(transactionType)) {
    requestJson.put("U_Z98_TYPE", transactionType);
}
```

## 分组示例

假设用户选择了以下4个单据：

| 单据号 | 账套 | 单据类型 | 事务类型 | 物料 |
|--------|------|----------|----------|------|
| RK001 | ACC001 | 1(入库) | RECEIVE | MAT001 |
| RK002 | ACC001 | 1(入库) | PURCHASE | MAT002 |
| CK001 | ACC001 | 2(出库) | SEND | MAT003 |
| CK002 | ACC001 | 2(出库) | TRANSFER | MAT004 |

### 优化前的处理
- 只会分成2组：
  - 组1：ACC001_1 (包含RK001, RK002) - 强制使用RECEIVE事务类型
  - 组2：ACC001_2 (包含CK001, CK002) - 强制使用SEND事务类型
- **问题**：RK002应该用PURCHASE事务类型，但被强制用了RECEIVE

### 优化后的处理
- 会分成4组：
  - 组1：ACC001-1-RECEIVE (包含RK001) - 使用RECEIVE事务类型 ✅
  - 组2：ACC001-1-PURCHASE (包含RK002) - 使用PURCHASE事务类型 ✅
  - 组3：ACC001-2-SEND (包含CK001) - 使用SEND事务类型 ✅
  - 组4：ACC001-2-TRANSFER (包含CK002) - 使用TRANSFER事务类型 ✅

## SAP请求结构

每个分组会生成独立的SAP请求：

### 组1请求 (ACC001-1-RECEIVE)
```json
{
  "DBName": "ACC001",
  "DocDate": "2024-12-28",
  "U_Z98_TYPE": "RECEIVE",
  "U_LYDH": "BATCH_1735123456789_RK001",
  "Lines": [
    {
      "ItemCode": "MAT001",
      "Quantity": 100.0,
      "Price": 10.5,
      "WarehouseCode": "WH001",
      "OBTNs": [
        {
          "DistNumber": "BATCH001",
          "Quantity": 100.0
        }
      ]
    }
  ]
}
```

### 组2请求 (ACC001-1-PURCHASE)
```json
{
  "DBName": "ACC001",
  "DocDate": "2024-12-28",
  "U_Z98_TYPE": "PURCHASE",
  "U_LYDH": "BATCH_1735123456790_RK002",
  "Lines": [
    {
      "ItemCode": "MAT002",
      "Quantity": 200.0,
      "Price": 15.0,
      "WarehouseCode": "WH001",
      "OBTNs": [
        {
          "DistNumber": "BATCH002",
          "Quantity": 200.0
        }
      ]
    }
  ]
}
```

## 优化效果

### 1. 数据准确性
- ✅ 每个单据使用正确的事务类型
- ✅ 避免事务类型混淆导致的SAP同步错误
- ✅ 保证业务逻辑的正确性

### 2. 灵活性
- ✅ 支持任意组合的单据批量处理
- ✅ 自动按业务规则分组
- ✅ 减少人工干预

### 3. 可维护性
- ✅ 分组逻辑清晰明确
- ✅ 错误信息包含详细的分组信息
- ✅ 便于问题排查和调试

## 日志输出示例

```
2024-12-28 10:30:15 INFO - 账套 [ACC001] 单据类型 [1] 事务类型 [RECEIVE] 的库存收发单批量同步成功
2024-12-28 10:30:16 INFO - 账套 [ACC001] 单据类型 [1] 事务类型 [PURCHASE] 的库存收发单批量同步成功
2024-12-28 10:30:17 INFO - 账套 [ACC001] 单据类型 [2] 事务类型 [SEND] 的库存收发单批量同步成功
2024-12-28 10:30:18 INFO - 账套 [ACC001] 单据类型 [2] 事务类型 [TRANSFER] 的库存收发单批量同步成功
```

## 测试验证

可以通过运行测试类 `WmsStockdReceiveSendBatchTest.testTransactionTypeGrouping()` 来验证分组逻辑：

```java
@Test
public void testTransactionTypeGrouping() {
    List<WmsStockdReceiveSend> testList = createDiverseTestData();

    // 模拟分组逻辑
    Map<String, List<WmsStockdReceiveSend>> groupMap = testList.stream()
        .collect(Collectors.groupingBy(item -> {
            String accountCode = item.getAccountCode() != null ? item.getAccountCode() : "DEFAULT";
            String billType = item.getBillType() != null ? item.getBillType() : "UNKNOWN";
            String transactionType = item.getTransactionType() != null ? item.getTransactionType() : "DEFAULT";
            return accountCode + "_" + billType + "_" + transactionType;
        }));

    // 验证分组结果
    System.out.println("总共选择了 " + testList.size() + " 个单据");
    System.out.println("分成了 " + groupMap.size() + " 个组进行SAP同步");
}
```

## 总结

通过这次优化，我们解决了事务类型混淆的问题，确保每个SAP请求都使用正确的事务类型，提高了数据的准确性和系统的可靠性。同时，分组逻辑更加清晰，便于维护和扩展。
